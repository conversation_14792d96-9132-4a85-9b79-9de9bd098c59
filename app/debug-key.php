<?php
/**
 * Script para debugar chave de API
 */

require_once __DIR__ . '/bootstrap.php';

$apiKey = 'e1copy_58211d1987fae226a0e2e73dd00ebe939aa9f';

echo "🔍 Debugando chave: $apiKey\n\n";

$db = Database::getInstance();

// 1. Verificar se a chave existe
echo "1️⃣ Verificando se a chave existe...\n";
$keyExists = $db->fetch("SELECT * FROM api_keys WHERE api_key = :key", ['key' => $apiKey]);

if ($keyExists) {
    echo "✅ Chave encontrada!\n";
    echo "📋 Dados da chave:\n";
    foreach ($keyExists as $field => $value) {
        echo "   $field: $value\n";
    }
} else {
    echo "❌ Chave NÃO encontrada!\n";
    
    // Listar todas as chaves para debug
    echo "\n📋 Chaves existentes no banco:\n";
    $allKeys = $db->fetchAll("SELECT api_key, name, status, user_id FROM api_keys LIMIT 10");
    foreach ($allKeys as $key) {
        echo "   {$key['api_key']} - {$key['name']} - {$key['status']} - User: {$key['user_id']}\n";
    }
    exit(1);
}

echo "\n";

// 2. Verificar usuário
echo "2️⃣ Verificando usuário...\n";
$user = $db->fetch("SELECT * FROM users WHERE id = :id", ['id' => $keyExists['user_id']]);

if ($user) {
    echo "✅ Usuário encontrado: {$user['name']} ({$user['email']}) - Status: {$user['status']}\n";
} else {
    echo "❌ Usuário NÃO encontrado!\n";
    exit(1);
}

echo "\n";

// 3. Verificar assinatura
echo "3️⃣ Verificando assinatura...\n";
$subscription = $db->fetch("SELECT * FROM subscriptions WHERE id = :id", ['id' => $keyExists['subscription_id']]);

if ($subscription) {
    echo "✅ Assinatura encontrada: Status: {$subscription['status']}\n";
    echo "   Início: {$subscription['starts_at']}\n";
    echo "   Fim: {$subscription['ends_at']}\n";
    echo "   Plano ID: {$subscription['plan_id']}\n";
} else {
    echo "❌ Assinatura NÃO encontrada!\n";
    exit(1);
}

echo "\n";

// 4. Testar verificação completa
echo "4️⃣ Testando verificação completa...\n";
$verification = Auth::verifyApiKey($apiKey);

echo "📊 Resultado da verificação:\n";
foreach ($verification as $field => $value) {
    echo "   $field: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . "\n";
}

if ($verification['valid']) {
    echo "\n✅ CHAVE VÁLIDA! ✅\n";
} else {
    echo "\n❌ CHAVE INVÁLIDA: {$verification['reason']} ❌\n";
}

echo "\n";

// 5. Testar endpoint diretamente
echo "5️⃣ Testando endpoint de validação...\n";

// Simular requisição
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';

// Criar controller e testar
$controller = new ApiController();

// Simular input JSON
$jsonData = json_encode(['api_key' => $apiKey]);
file_put_contents('php://temp', $jsonData);

try {
    ob_start();
    $result = $controller->validateKey();
    $output = ob_get_clean();
    
    echo "📤 Saída do endpoint:\n";
    echo $result . "\n";
    
    if ($output) {
        echo "📝 Output adicional: $output\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erro no endpoint: " . $e->getMessage() . "\n";
}

echo "\n🎯 Debug concluído!\n";
?>
