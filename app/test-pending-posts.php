<?php
/**
 * Teste da API de Posts Pendentes
 */

// Incluir bootstrap
require_once __DIR__ . '/bootstrap.php';

echo "=== Teste da API de Posts Pendentes ===\n\n";

try {
    // Instanciar o controller
    $apiController = new ApiController();
    
    echo "1. Testando método getPendingPosts()...\n";
    
    // Capturar a saída
    ob_start();
    $apiController->getPendingPosts();
    $output = ob_get_clean();
    
    echo "2. Saída capturada:\n";
    echo $output . "\n\n";
    
    // Tentar decodificar JSON
    $data = json_decode($output, true);
    
    if ($data) {
        echo "3. JSON válido!\n";
        echo "   - Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        
        if (isset($data['data'])) {
            echo "   - Total sites: " . ($data['data']['total_sites'] ?? 0) . "\n";
            echo "   - Total posts: " . ($data['data']['total_posts'] ?? 0) . "\n";
            echo "   - Timestamp: " . ($data['data']['timestamp'] ?? 'N/A') . "\n";
        }
    } else {
        echo "3. Erro ao decodificar JSON\n";
        echo "   Erro: " . json_last_error_msg() . "\n";
    }
    
} catch (Exception $e) {
    echo "Erro: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== Fim do Teste ===\n";
