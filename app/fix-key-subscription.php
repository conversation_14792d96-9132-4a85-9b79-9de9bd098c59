<?php
/**
 * Script para corrigir a assinatura da chave específica
 */

require_once __DIR__ . '/bootstrap.php';

$apiKey = 'e1copy_9f61f8b3ad6a29b10c0b76a6e401bd1a95ae685b82e539bad6592415c7b86715';

echo "🔧 Corrigindo assinatura para a chave: " . substr($apiKey, 0, 20) . "...\n\n";

$db = Database::getInstance();

try {
    $db->beginTransaction();
    
    // 1. Buscar dados da chave
    $keyData = $db->fetch(
        "SELECT ak.*, u.name as user_name, u.email, u.status as user_status, s.status as subscription_status
         FROM api_keys ak
         JOIN users u ON ak.user_id = u.id
         JOIN subscriptions s ON ak.subscription_id = s.id
         WHERE ak.api_key = :key",
        ['key' => $apiKey]
    );
    
    if (!$keyData) {
        echo "❌ Chave não encontrada!\n";
        exit(1);
    }
    
    echo "📋 Status atual:\n";
    echo "   Usuário: {$keyData['user_name']} ({$keyData['email']})\n";
    echo "   Status usuário: {$keyData['user_status']}\n";
    echo "   Status chave: {$keyData['status']}\n";
    echo "   Status assinatura: {$keyData['subscription_status']}\n\n";
    
    $needsUpdate = false;
    
    // 2. Corrigir status do usuário se necessário
    if ($keyData['user_status'] !== 'active') {
        echo "🔧 Ativando usuário...\n";
        $db->update('users', ['status' => 'active'], 'id = :id', ['id' => $keyData['user_id']]);
        $needsUpdate = true;
    }
    
    // 3. Corrigir status da chave se necessário
    if ($keyData['status'] !== 'active') {
        echo "🔧 Ativando chave API...\n";
        $db->update('api_keys', ['status' => 'active'], 'id = :id', ['id' => $keyData['id']]);
        $needsUpdate = true;
    }
    
    // 4. Corrigir status da assinatura se necessário
    if ($keyData['subscription_status'] !== 'active') {
        echo "🔧 Ativando assinatura...\n";
        $db->update('subscriptions', [
            'status' => 'active',
            'payment_status' => 'paid'
        ], 'id = :id', ['id' => $keyData['subscription_id']]);
        $needsUpdate = true;
    }
    
    if ($needsUpdate) {
        $db->commit();
        echo "✅ Correções aplicadas com sucesso!\n\n";
        
        // 5. Testar novamente
        echo "🧪 Testando validação após correção...\n";
        $verification = Auth::verifyApiKey($apiKey);
        
        if ($verification['valid']) {
            echo "✅ SUCESSO! Chave agora está válida!\n";
        } else {
            echo "❌ Ainda há problemas: {$verification['reason']}\n";
        }
    } else {
        $db->rollback();
        echo "ℹ️ Nenhuma correção necessária. Todos os status já estão corretos.\n";
        
        // Testar mesmo assim
        echo "🧪 Testando validação...\n";
        $verification = Auth::verifyApiKey($apiKey);
        
        if ($verification['valid']) {
            echo "✅ Chave está válida!\n";
        } else {
            echo "❌ Problema persistente: {$verification['reason']}\n";
        }
    }
    
} catch (Exception $e) {
    $db->rollback();
    echo "❌ ERRO: " . $e->getMessage() . "\n";
    exit(1);
}
