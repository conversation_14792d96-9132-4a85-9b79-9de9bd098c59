<?php
/**
 * Teste das novas funcionalidades da API
 * Demonstra a organização de posts por cliente e por site
 */

echo "=== TESTE DA NOVA ESTRUTURA DE API ===\n\n";

// Simular dados de resposta da API
$mockData = [
    'summary' => [
        'total_sites' => 3,
        'total_posts' => 5,
        'active_sites' => 2,
        'active_clients' => 2
    ],
    'posts_by_client' => [
        [
            'client_info' => [
                'user_name' => 'E1Cursos',
                'user_email' => '<EMAIL>'
            ],
            'sites' => [
                [
                    'site_id' => 1,
                    'site_name' => 'E1Cursos',
                    'site_url' => 'https://e1cursos.com',
                    'posts_count' => 2,
                    'status' => 'success'
                ]
            ],
            'total_posts' => 2,
            'posts' => [
                [
                    'id' => 1,
                    'title' => 'Lençol Impermeável Matizado Com Elástico Em Toda Volta',
                    'post_type' => 'product',
                    'created_at' => '2025-06-20 11:08:19'
                ],
                [
                    'id' => 2,
                    'title' => 'Short Calção Bermuda RUN 5 Em 1 Masculino Academia',
                    'post_type' => 'product',
                    'created_at' => '2025-06-20 09:06:20'
                ]
            ]
        ],
        [
            'client_info' => [
                'user_name' => 'Esmael Silva',
                'user_email' => '<EMAIL>'
            ],
            'sites' => [
                [
                    'site_id' => 2,
                    'site_name' => 'Melhor Cupom',
                    'site_url' => 'https://melhorcupom.shop',
                    'posts_count' => 3,
                    'status' => 'success'
                ]
            ],
            'total_posts' => 3,
            'posts' => [
                [
                    'id' => 3,
                    'title' => 'Produto A',
                    'post_type' => 'product',
                    'created_at' => '2025-06-20 10:00:00'
                ],
                [
                    'id' => 4,
                    'title' => 'Produto B',
                    'post_type' => 'product',
                    'created_at' => '2025-06-20 10:30:00'
                ],
                [
                    'id' => 5,
                    'title' => 'Produto C',
                    'post_type' => 'product',
                    'created_at' => '2025-06-20 11:00:00'
                ]
            ]
        ]
    ],
    'posts_by_site' => [
        [
            'site_info' => [
                'site_id' => 1,
                'site_name' => 'E1Cursos',
                'site_url' => 'https://e1cursos.com',
                'user_name' => 'E1Cursos',
                'user_email' => '<EMAIL>'
            ],
            'posts' => [
                [
                    'id' => 1,
                    'title' => 'Lençol Impermeável Matizado Com Elástico Em Toda Volta',
                    'post_type' => 'product',
                    'created_at' => '2025-06-20 11:08:19'
                ],
                [
                    'id' => 2,
                    'title' => 'Short Calção Bermuda RUN 5 Em 1 Masculino Academia',
                    'post_type' => 'product',
                    'created_at' => '2025-06-20 09:06:20'
                ]
            ],
            'total_posts' => 2,
            'status' => 'success'
        ],
        [
            'site_info' => [
                'site_id' => 2,
                'site_name' => 'Melhor Cupom',
                'site_url' => 'https://melhorcupom.shop',
                'user_name' => 'Esmael Silva',
                'user_email' => '<EMAIL>'
            ],
            'posts' => [
                [
                    'id' => 3,
                    'title' => 'Produto A',
                    'post_type' => 'product',
                    'created_at' => '2025-06-20 10:00:00'
                ],
                [
                    'id' => 4,
                    'title' => 'Produto B',
                    'post_type' => 'product',
                    'created_at' => '2025-06-20 10:30:00'
                ],
                [
                    'id' => 5,
                    'title' => 'Produto C',
                    'post_type' => 'product',
                    'created_at' => '2025-06-20 11:00:00'
                ]
            ],
            'total_posts' => 3,
            'status' => 'success'
        ]
    ],
    'timestamp' => '2025-06-20 11:38:00'
];

try {

    echo "1. Estrutura completa da API - getPendingPosts():\n";
    echo "=" . str_repeat("=", 50) . "\n";

    $data = $mockData;
    
    echo "Summary:\n";
    print_r($data['summary'] ?? []);
    
    echo "\nPosts por Cliente (" . count($data['posts_by_client'] ?? []) . " clientes):\n";
    foreach ($data['posts_by_client'] ?? [] as $i => $client) {
        echo "Cliente " . ($i + 1) . ":\n";
        echo "  - Nome: " . $client['client_info']['user_name'] . "\n";
        echo "  - Email: " . $client['client_info']['user_email'] . "\n";
        echo "  - Sites: " . count($client['sites']) . "\n";
        echo "  - Total Posts: " . $client['total_posts'] . "\n";
        
        foreach ($client['sites'] as $j => $site) {
            echo "    Site " . ($j + 1) . ": " . $site['site_name'] . " (" . $site['posts_count'] . " posts)\n";
        }
        echo "\n";
    }
    
    echo "\nPosts por Site (" . count($data['posts_by_site'] ?? []) . " sites):\n";
    foreach ($data['posts_by_site'] ?? [] as $i => $site) {
        echo "Site " . ($i + 1) . ":\n";
        echo "  - Nome: " . $site['site_info']['site_name'] . "\n";
        echo "  - URL: " . $site['site_info']['site_url'] . "\n";
        echo "  - Cliente: " . $site['site_info']['user_name'] . "\n";
        echo "  - Posts: " . $site['total_posts'] . "\n";
        echo "  - Status: " . $site['status'] . "\n\n";
    }
    
    echo "\n2. Estrutura para N8N - getPendingPostsByClient():\n";
    echo "=" . str_repeat("=", 50) . "\n";

    $clientData = [
        'clients' => $data['posts_by_client'],
        'summary' => $data['summary'],
        'timestamp' => $data['timestamp']
    ];

    echo "Clientes encontrados: " . count($clientData['clients'] ?? []) . "\n";
    echo "Summary: ";
    print_r($clientData['summary'] ?? []);

    echo "\nDetalhes dos clientes:\n";
    foreach ($clientData['clients'] as $i => $client) {
        echo "Cliente " . ($i + 1) . ": " . $client['client_info']['user_name'] . "\n";
        echo "  - Email: " . $client['client_info']['user_email'] . "\n";
        echo "  - Posts: " . $client['total_posts'] . "\n";
        echo "  - Sites: " . count($client['sites']) . "\n\n";
    }

    echo "\n3. Estrutura para N8N - getPendingPostsBySite():\n";
    echo "=" . str_repeat("=", 50) . "\n";

    $siteData = [
        'sites' => $data['posts_by_site'],
        'summary' => $data['summary'],
        'timestamp' => $data['timestamp']
    ];

    echo "Sites encontrados: " . count($siteData['sites'] ?? []) . "\n";
    echo "Summary: ";
    print_r($siteData['summary'] ?? []);

    echo "\nDetalhes dos sites:\n";
    foreach ($siteData['sites'] as $i => $site) {
        echo "Site " . ($i + 1) . ": " . $site['site_info']['site_name'] . "\n";
        echo "  - URL: " . $site['site_info']['site_url'] . "\n";
        echo "  - Cliente: " . $site['site_info']['user_name'] . "\n";
        echo "  - Posts: " . $site['total_posts'] . "\n\n";
    }
    
    echo "\n=== TESTE CONCLUÍDO COM SUCESSO ===\n";
    
} catch (Exception $e) {
    echo "ERRO: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
