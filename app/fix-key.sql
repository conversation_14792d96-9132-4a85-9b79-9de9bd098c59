-- Script SQL para verificar e corrigir a chave específica
-- Execute este script no seu banco de dados MySQL

-- 1. Verificar status atual da chave
SELECT 
    'Status Atual' as tipo,
    ak.api_key,
    ak.status as key_status,
    u.name as user_name,
    u.email,
    u.status as user_status,
    s.status as subscription_status,
    s.payment_status,
    p.name as plan_name
FROM api_keys ak
JOIN users u ON ak.user_id = u.id
JOIN subscriptions s ON ak.subscription_id = s.id
JOIN plans p ON s.plan_id = p.id
WHERE ak.api_key = 'e1copy_9f61f8b3ad6a29b10c0b76a6e401bd1a95ae685b82e539bad6592415c7b86715';

-- 2. Ativar usuário se necessário
UPDATE users 
SET status = 'active' 
WHERE id IN (
    SELECT user_id 
    FROM api_keys 
    WHERE api_key = 'e1copy_9f61f8b3ad6a29b10c0b76a6e401bd1a95ae685b82e539bad6592415c7b86715'
) AND status != 'active';

-- 3. Ativar chave API se necessário
UPDATE api_keys 
SET status = 'active' 
WHERE api_key = 'e1copy_9f61f8b3ad6a29b10c0b76a6e401bd1a95ae685b82e539bad6592415c7b86715' 
AND status != 'active';

-- 4. Ativar assinatura se necessário
UPDATE subscriptions 
SET status = 'active', payment_status = 'paid'
WHERE id IN (
    SELECT subscription_id 
    FROM api_keys 
    WHERE api_key = 'e1copy_9f61f8b3ad6a29b10c0b76a6e401bd1a95ae685b82e539bad6592415c7b86715'
) AND status != 'active';

-- 5. Verificar status após correção
SELECT 
    'Status Após Correção' as tipo,
    ak.api_key,
    ak.status as key_status,
    u.name as user_name,
    u.email,
    u.status as user_status,
    s.status as subscription_status,
    s.payment_status,
    p.name as plan_name
FROM api_keys ak
JOIN users u ON ak.user_id = u.id
JOIN subscriptions s ON ak.subscription_id = s.id
JOIN plans p ON s.plan_id = p.id
WHERE ak.api_key = 'e1copy_9f61f8b3ad6a29b10c0b76a6e401bd1a95ae685b82e539bad6592415c7b86715';
