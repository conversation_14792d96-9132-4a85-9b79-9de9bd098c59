# E1Copy AI Dashboard

Sistema de gerenciamento de clientes e API para o plugin WordPress E1Copy AI.

## 🚀 Instalação

### 1. Configurar Banco de Dados

Edite o arquivo `.env` com suas credenciais do banco de dados:

```env
DB_HOST=localhost
DB_NAME=u880879026_appDash
DB_USER=u880879026_userDash
DB_PASS=:sX=zys@2
APP_URL=https://app.melhorcupom.shop
```

### 2. Executar Configuração Inicial

Execute o script de configuração para criar as tabelas e dados iniciais:

```bash
php setup.php
```

### 3. Testar o Sistema

Execute o script de teste para verificar se tudo está funcionando:

```bash
php test.php
```

## 🔐 Acesso ao Sistema

### Administrador
- **URL**: https://app.melhorcupom.shop/admin/login
- **Email**: <EMAIL>
- **Senha**: admin123

### Cliente
- **URL**: https://app.melhorcupom.shop/login
- **Credenciais**: Criadas pelo administrador

## 📋 Funcionalidades

### Área do Cliente
- ✅ Dashboard com estatísticas de uso
- ✅ Gerenciamento de chaves de API
- ✅ Página "Minha Conta"
- ✅ Visualização de plano e limites

### Área Administrativa
- ✅ Dashboard com métricas do sistema
- ✅ Gerenciamento de clientes
- ✅ Configurações do sistema
- ✅ Controle de chaves de API
- ✅ Logs de atividade

### API
- ✅ Verificação de chaves de API
- ✅ Geração de conteúdo com IA (Groq)
- ✅ Screenshots (ApiFlash)
- ✅ Controle de uso e limites
- ✅ Logs de requisições

## 🔧 Configuração das APIs

Após o login como administrador, configure as chaves das APIs externas:

1. Acesse **Configurações** no painel admin
2. Configure:
   - **Groq API Key**: Para geração de conteúdo
   - **ApiFlash Key**: Para screenshots

## 🔗 Endpoints da API

### Verificar Chave
```
POST /api/v1/verify-key
{
    "api_key": "e1copy_..."
}
```

### Gerar Conteúdo
```
POST /api/v1/generate-content
{
    "api_key": "e1copy_...",
    "prompt": "Escreva sobre..."
}
```

### Screenshot
```
POST /api/v1/screenshot
{
    "api_key": "e1copy_...",
    "url": "https://exemplo.com"
}
```

### Health Check
```
GET /api/v1/health
```

## 📊 Estrutura do Banco

- **users**: Clientes e administradores
- **plans**: Planos disponíveis
- **subscriptions**: Assinaturas dos clientes
- **api_keys**: Chaves de API dos clientes
- **api_usage**: Log de uso da API
- **settings**: Configurações do sistema
- **login_attempts**: Controle de segurança

## 🔒 Segurança

- ✅ Hash de senhas com Argon2ID
- ✅ Proteção contra brute force
- ✅ Tokens CSRF
- ✅ Validação de entrada
- ✅ Controle de acesso por roles
- ✅ Rate limiting da API

## 🛠️ Migração do Plugin

Para migrar a API do plugin WordPress:

1. Substitua as URLs da API no plugin para apontar para este sistema
2. Use as chaves de API geradas aqui
3. Mantenha a mesma estrutura de requisições

## 📝 Logs

Os logs de atividade são armazenados na tabela `api_usage` e podem ser visualizados no painel administrativo.

## 🆘 Suporte

Em caso de problemas:

1. Verifique os logs do servidor
2. Execute `php test.php` para diagnosticar
3. Verifique as configurações no arquivo `.env`
4. Confirme se o banco de dados está acessível

## 📄 Licença

Sistema proprietário - E1Copy AI © 2024
