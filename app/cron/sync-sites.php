#!/usr/bin/env php
<?php
/**
 * Script de Sincronização de Sites
 * Executa sincronização automática dos sites WordPress
 * 
 * Uso: php sync-sites.php
 * Cron: 0,15,30,45 * * * * php /path/to/app/cron/sync-sites.php
 */

// Definir diretório base
$appRoot = dirname(__DIR__);
chdir($appRoot);

// Carregar bootstrap
require_once $appRoot . '/bootstrap.php';

// Arquivo de lock para evitar execuções simultâneas
$lockFile = $appRoot . '/cron/sync-sites.lock';
$logFile = $appRoot . '/cron/sync-sites.log';

// Função para log
function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[{$timestamp}] {$message}" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    echo $logEntry;
}

// Verificar se já está executando
if (file_exists($lockFile)) {
    $lockTime = filemtime($lockFile);
    $currentTime = time();
    
    // Se o lock tem mais de 30 minutos, considerar travado e remover
    if (($currentTime - $lockTime) > 1800) {
        unlink($lockFile);
        logMessage("Lock antigo removido (mais de 30 minutos)");
    } else {
        logMessage("Sincronização já está executando. Saindo...");
        exit(0);
    }
}

// Criar arquivo de lock
file_put_contents($lockFile, getmypid());

try {
    logMessage("=== Iniciando sincronização de sites ===");
    
    // Verificar se as tabelas necessárias existem
    $db = Database::getInstance();
    
    try {
        $db->fetch("SELECT 1 FROM client_sites LIMIT 1");
    } catch (Exception $e) {
        logMessage("Tabela client_sites não encontrada. Saindo...");
        exit(0);
    }
    
    // Inicializar serviço de sincronização
    $syncService = new SiteSyncService();
    
    // Buscar sites que precisam de sincronização
    // Priorizar sites que não foram verificados nas últimas 2 horas
    $sites = $db->fetchAll("
        SELECT cs.*, u.name as user_name, ak.api_key
        FROM client_sites cs
        LEFT JOIN users u ON cs.user_id = u.id
        LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
        WHERE cs.status IN ('connected', 'pending', 'disconnected')
        AND u.status = 'active'
        AND (
            cs.last_connection IS NULL 
            OR cs.last_connection < DATE_SUB(NOW(), INTERVAL 2 HOUR)
        )
        ORDER BY 
            CASE 
                WHEN cs.last_connection IS NULL THEN 0
                ELSE 1 
            END,
            cs.last_connection ASC
        LIMIT 10
    ");
    
    if (empty($sites)) {
        logMessage("Nenhum site precisa de sincronização no momento");
        exit(0);
    }
    
    logMessage("Encontrados " . count($sites) . " sites para sincronizar");
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($sites as $site) {
        $siteUrl = $site['site_url'];
        logMessage("Sincronizando: {$siteUrl}");
        
        try {
            $result = $syncService->syncSite($site);
            
            if ($result['success']) {
                $successCount++;
                $message = "✅ {$siteUrl}: {$result['message']}";
                if (isset($result['data']['pending_posts']) && $result['data']['pending_posts'] > 0) {
                    $addedToQueue = $result['data']['added_to_queue'] ?? 0;
                    $message .= " ({$result['data']['pending_posts']} posts pendentes, {$addedToQueue} adicionados à fila)";
                }
                logMessage($message);
            } else {
                $errorCount++;
                logMessage("❌ {$siteUrl}: {$result['message']}");
            }
            
        } catch (Exception $e) {
            $errorCount++;
            logMessage("❌ {$siteUrl}: Erro na sincronização - " . $e->getMessage());
        }
        
        // Pequena pausa entre sites para não sobrecarregar
        sleep(2);
    }
    
    logMessage("=== Sincronização concluída ===");
    logMessage("Sucessos: {$successCount}, Erros: {$errorCount}");
    
    // Limpeza de logs antigos (manter apenas últimos 7 dias)
    $logLines = file($logFile);
    if (count($logLines) > 1000) {
        $recentLines = array_slice($logLines, -500);
        file_put_contents($logFile, implode('', $recentLines));
        logMessage("Log limpo - mantidas últimas 500 linhas");
    }
    
} catch (Exception $e) {
    logMessage("ERRO CRÍTICO: " . $e->getMessage());
    logMessage("Stack trace: " . $e->getTraceAsString());
} finally {
    // Remover arquivo de lock
    if (file_exists($lockFile)) {
        unlink($lockFile);
    }
}

logMessage("Script finalizado\n");
?>
