# Sistema de Geração de Conteúdo com IA - E1Copy AI

## 🎯 Visão Geral

Sistema completo de geração automática de conteúdo usando IA Groq, integrado ao dashboard E1Copy AI e plugin WordPress.

## 🚀 Funcionalidades Implementadas

### 1. 📊 **Painel Administrativo - Menu Relatórios**

#### **Posts Clients** (`/admin/reports/posts-clients`)
- ✅ Lista todos os sites/clientes que utilizam o plugin
- ✅ Estatísticas por cliente:
  - Nome do site/domínio
  - Quantidade de posts gerados
  - Posts pendentes na fila
  - Status do último post
  - Data da última execução
- ✅ Filtros e ordenação por nome, data, status
- ✅ Badges coloridos para status (verde/amarelo/vermelho)
- ✅ Botões de ação para gerar posts

#### **Uso da IA** (`/admin/reports/ai-usage`)
- ✅ Métricas em tempo real
- ✅ Logs de execução detalhados
- ✅ Posts gerados recentemente
- ✅ Ranking de usuários por geração
- ✅ Botão para processar fila manualmente
- ✅ Auto-refresh a cada 30 segundos

### 2. ⚙️ **Configurações de Geração de Conteúdo**

#### **Configurações Gerais** (`/admin/ai-settings`)
- ✅ **API Groq**: Chave, modelo, tokens, temperatura
- ✅ **Padrões**: Template padrão, variáveis obrigatórias
- ✅ **Limites**: Posts por dia, tentativas máximas
- ✅ **Fila**: Habilitação do processamento automático

#### **Templates HTML** (Modal integrado)
- ✅ Criação de templates personalizados
- ✅ Variáveis dinâmicas: `{{titulo}}`, `{{conteudo}}`, etc.
- ✅ Definição de variáveis obrigatórias
- ✅ Template padrão configurável
- ✅ Ativação/desativação de templates

### 3. 🤖 **Integração com API Groq**

#### **Serviço GroqService** (`app/services/GroqService.php`)
- ✅ Conexão com API Groq
- ✅ Geração de conteúdo baseada em prompts
- ✅ Aplicação de templates HTML
- ✅ Processamento de variáveis dinâmicas
- ✅ Tratamento de erros e fallbacks

#### **Variáveis Suportadas**
- ✅ `{{titulo}}` - Título do post
- ✅ `{{subtitulo}}` - Subtítulo
- ✅ `{{conteudo}}` - Conteúdo principal em HTML
- ✅ `{{resumo}}` - Excerpt/resumo
- ✅ `{{slug}}` - URL slug
- ✅ `{{categoria}}` - Categoria principal
- ✅ `{{tags}}` - Tags separadas por vírgula
- ✅ `{{palavra_chave}}` - Palavra-chave principal

### 4. 📋 **Sistema de Fila de Processamento**

#### **ContentQueueService** (`app/services/ContentQueueService.php`)
- ✅ Fila em banco de dados
- ✅ Priorização de itens
- ✅ Agendamento de execução
- ✅ Controle de tentativas
- ✅ Processamento em lote
- ✅ Limpeza automática de itens antigos

#### **Funcionalidades da Fila**
- ✅ Adicionar posts à fila
- ✅ Processar automaticamente via cron
- ✅ Reprocessar itens falhados
- ✅ Cancelar itens pendentes
- ✅ Estatísticas em tempo real

### 5. 🔗 **Integração WordPress**

#### **Endpoints do Plugin** (e1copy-ai.php)
- ✅ `e1copy_get_site_data` - Coleta dados do site
- ✅ `e1copy_receive_post` - Recebe posts gerados
- ✅ Verificação de licença em todos os endpoints
- ✅ Sanitização e validação de dados
- ✅ Criação automática de categorias/tags

#### **Dados Coletados do Site**
- ✅ Nome e descrição do site
- ✅ Categorias e tags existentes
- ✅ Posts recentes
- ✅ Estatísticas gerais
- ✅ Informações do tema e WordPress

### 6. 📈 **Sistema de Monitoramento**

#### **Banco de Dados**
- ✅ `generated_posts` - Posts gerados
- ✅ `content_queue` - Fila de processamento
- ✅ `content_templates` - Templates HTML
- ✅ `ai_settings` - Configurações da IA
- ✅ `ai_execution_logs` - Logs de execução

#### **Logs e Tracking**
- ✅ Log de todas as execuções
- ✅ Tempo de execução em milissegundos
- ✅ Status de sucesso/erro
- ✅ Rastreamento por usuário e site
- ✅ Histórico completo de ações

### 7. 🔄 **Processamento Automático**

#### **Script Cron** (`app/cron/process-queue.php`)
- ✅ Processamento automático da fila
- ✅ Lock para evitar execuções simultâneas
- ✅ Log detalhado de execuções
- ✅ Limpeza automática de itens antigos
- ✅ Configuração via crontab: `*/5 * * * *`

## 🛠️ **Instalação e Configuração**

### 1. **Banco de Dados**
```sql
-- Execute o arquivo SQL
mysql -u usuario -p database < app/install-ai-system.sql
```

### 2. **Configuração da API Groq**
1. Acesse `/admin/ai-settings`
2. Configure sua chave da API Groq
3. Ajuste modelo e parâmetros conforme necessário

### 3. **Configuração do Cron**
```bash
# Adicionar ao crontab
*/5 * * * * php /path/to/app/cron/process-queue.php
```

### 4. **Permissões**
- Certifique-se que o diretório `app/cron/` tem permissão de escrita
- Configure logs em `app/cron/queue-process.log`

## 📡 **API Endpoints**

### **Dashboard → WordPress**
- `POST /api/content/add-to-queue` - Adicionar à fila
- `POST /api/content/generate-now` - Gerar imediatamente
- `POST /api/content/publish` - Publicar no WordPress
- `GET /api/content/posts` - Listar posts gerados
- `POST /api/content/process-queue` - Processar fila (admin)

### **WordPress → Dashboard**
- `wp-admin/admin-ajax.php?action=e1copy_get_site_data`
- `wp-admin/admin-ajax.php?action=e1copy_receive_post`

## 🎨 **Interface do Usuário**

### **Menu Administrativo**
```
Dashboard
├── Relatórios
│   ├── Posts Clients
│   └── Uso da IA
└── Configurações
    ├── Gerais
    └── Geração de Conteúdo
```

### **Funcionalidades por Página**
- **Posts Clients**: Monitoramento de sites e estatísticas
- **Uso da IA**: Logs, métricas e controle da fila
- **Configurações**: API Groq, templates e limites

## 🔧 **Fluxo de Funcionamento**

1. **Administrador** configura API Groq e templates
2. **Sistema** coleta dados dos sites WordPress
3. **Fila** processa posts baseados em tópicos/palavras-chave
4. **IA Groq** gera conteúdo usando templates
5. **WordPress** recebe e publica posts automaticamente
6. **Dashboard** monitora todo o processo

## 📊 **Métricas Disponíveis**

- Total de posts gerados
- Taxa de sucesso/falha
- Posts publicados vs. rascunhos
- Tempo médio de geração
- Uso por cliente/site
- Itens na fila
- Logs de execução em tempo real

## 🎯 **Próximos Passos**

1. ✅ **Sistema completo implementado**
2. 🔄 **Teste em ambiente de produção**
3. 📈 **Monitoramento de performance**
4. 🚀 **Otimizações baseadas no uso real**

---

**Sistema E1Copy AI - Geração Automática de Conteúdo com IA** 🤖✨
