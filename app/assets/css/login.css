/* CSS Local para páginas de login - E1Copy AI */

/* Reset básico */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
    line-height: 1.5;
    color: #212529;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.justify-content-center {
    justify-content: center;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

.col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

@media (max-width: 768px) {
    .col-md-6, .col-lg-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.login-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
    max-width: 400px;
    width: 100%;
    margin: 0 auto;
}

.login-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.login-header.admin {
    background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
}

.login-body {
    padding: 2rem;
}

h3 {
    font-size: 1.75rem;
    font-weight: 500;
    margin-bottom: 0;
}

p {
    margin-bottom: 1rem;
}

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.me-1 { margin-right: 0.25rem !important; }
.me-2 { margin-right: 0.5rem !important; }

.opacity-75 { opacity: 0.75; }

.admin-badge {
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: inline-block;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #6c757d;
    text-align: center;
    white-space: nowrap;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 0.375rem 0 0 0.375rem;
    border-right: none;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ced4da;
    border-radius: 0 0.375rem 0.375rem 0;
    border-left: none;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: #212529;
    background-color: #fff;
    border-color: #667eea;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    border-left: none;
}

.form-control.admin:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.d-grid {
    display: grid;
}

.btn {
    display: inline-block;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 0.75rem;
    font-weight: 500;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.btn-primary.admin {
    background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
}

.btn-primary.admin:hover {
    background: linear-gradient(135deg, #c82333 0%, #5a2d91 100%);
}

.btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.25rem;
    border-radius: 0.5rem;
}

.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.5rem;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.text-center {
    text-align: center;
}

.text-muted {
    color: #6c757d;
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.5);
}

.text-decoration-none {
    text-decoration: none;
}

.invalid-feedback {
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.d-block {
    display: block;
}

.is-invalid {
    border-color: #dc3545;
}

/* Font Awesome Icons (básicos) */
.fas::before {
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
}

.fa-robot::before { content: "\f544"; }
.fa-shield-alt::before { content: "\f3ed"; }
.fa-envelope::before { content: "\f0e0"; }
.fa-lock::before { content: "\f023"; }
.fa-sign-in-alt::before { content: "\f2f6"; }
.fa-user-shield::before { content: "\f505"; }
.fa-key::before { content: "\f084"; }
.fa-crown::before { content: "\f521"; }
.fa-exclamation-circle::before { content: "\f06a"; }
.fa-check-circle::before { content: "\f058"; }
.fa-arrow-left::before { content: "\f060"; }

/* Fallback se Font Awesome não carregar */
.fas {
    display: inline-block;
    width: 1em;
    text-align: center;
}

a {
    color: #667eea;
    text-decoration: none;
}

a:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

small {
    font-size: 0.875em;
}
