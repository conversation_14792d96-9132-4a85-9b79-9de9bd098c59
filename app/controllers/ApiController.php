<?php
/**
 * Controller da API
 * Sistema de Dashboard E1Copy AI
 */

class ApiController extends Controller {
    
    public function verifyKey() {
        $apiKey = $this->input('api_key') ?: $this->input('key');
        
        if (!$apiKey) {
            return ApiResponse::error('Chave de API não fornecida', 400);
        }
        
        $verification = Auth::verifyApiKey($apiKey);
        
        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }
        
        // Log da verificação
        Auth::logApiUsage(
            $verification['key_id'],
            $verification['user_id'],
            '/api/v1/verify-key',
            $_SERVER['REQUEST_METHOD'],
            200,
            0
        );
        
        return ApiResponse::success([
            'valid' => true,
            'user_id' => $verification['user_id'],
            'monthly_usage' => $verification['monthly_usage'],
            'monthly_limit' => $verification['monthly_limit'],
            'remaining' => $verification['monthly_limit'] ? 
                          ($verification['monthly_limit'] - $verification['monthly_usage']) : null
        ]);
    }
    
    public function clientStatus($apiKey) {
        $verification = Auth::verifyApiKey($apiKey);
        
        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }
        
        // Buscar informações do cliente
        $client = $this->db->fetch(
            "SELECT u.*, s.status as subscription_status, s.ends_at, p.name as plan_name
             FROM users u
             JOIN subscriptions s ON u.id = s.user_id
             JOIN plans p ON s.plan_id = p.id
             WHERE u.id = :user_id AND s.status = 'active'
             ORDER BY s.created_at DESC
             LIMIT 1",
            ['user_id' => $verification['user_id']]
        );
        
        if (!$client) {
            return ApiResponse::error('Cliente não encontrado', 404);
        }
        
        return ApiResponse::success([
            'client' => [
                'name' => $client['name'],
                'email' => $client['email'],
                'status' => $client['status'],
                'plan' => $client['plan_name'],
                'subscription_status' => $client['subscription_status'],
                'expires_at' => $client['ends_at']
            ],
            'usage' => [
                'monthly_usage' => $verification['monthly_usage'],
                'monthly_limit' => $verification['monthly_limit']
            ]
        ]);
    }
    
    public function suspendKey() {
        $apiKey = $this->input('api_key');
        $adminKey = $this->input('admin_key');
        
        // Verificar se é uma requisição administrativa válida
        // Por segurança, apenas admins podem suspender chaves
        if (!$adminKey || $adminKey !== config('app.admin_api_key')) {
            return ApiResponse::forbidden('Acesso negado');
        }
        
        if (!$apiKey) {
            return ApiResponse::error('Chave de API não fornecida', 400);
        }
        
        $result = $this->db->update(
            'api_keys',
            ['status' => 'suspended'],
            'api_key = :key',
            ['key' => $apiKey]
        );
        
        if ($result) {
            return ApiResponse::success(['message' => 'Chave suspensa com sucesso']);
        } else {
            return ApiResponse::error('Erro ao suspender chave', 500);
        }
    }
    
    public function activateKey() {
        $apiKey = $this->input('api_key');
        $adminKey = $this->input('admin_key');
        
        // Verificar se é uma requisição administrativa válida
        if (!$adminKey || $adminKey !== config('app.admin_api_key')) {
            return ApiResponse::forbidden('Acesso negado');
        }
        
        if (!$apiKey) {
            return ApiResponse::error('Chave de API não fornecida', 400);
        }
        
        $result = $this->db->update(
            'api_keys',
            ['status' => 'active'],
            'api_key = :key',
            ['key' => $apiKey]
        );
        
        if ($result) {
            return ApiResponse::success(['message' => 'Chave ativada com sucesso']);
        } else {
            return ApiResponse::error('Erro ao ativar chave', 500);
        }
    }
    
    public function usageStats($apiKey) {
        $verification = Auth::verifyApiKey($apiKey);
        
        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }
        
        // Estatísticas de uso
        $stats = $this->db->fetch(
            "SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_requests,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as week_requests,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as month_requests,
                AVG(response_time_ms) as avg_response_time
             FROM api_usage 
             WHERE api_key_id = :key_id",
            ['key_id' => $verification['key_id']]
        );
        
        return ApiResponse::success($stats);
    }
    
    public function generateContent() {
        $apiKey = $this->input('api_key');
        $prompt = $this->input('prompt');
        
        if (!$apiKey || !$prompt) {
            return ApiResponse::error('Parâmetros obrigatórios: api_key, prompt', 400);
        }
        
        $startTime = microtime(true);
        $verification = Auth::verifyApiKey($apiKey);
        
        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }
        
        // Buscar configurações da API Groq
        $groqKey = $this->db->fetch(
            "SELECT value FROM settings WHERE key_name = 'groq_api_key'"
        )['value'] ?? '';
        
        if (!$groqKey) {
            return ApiResponse::error('API Groq não configurada', 500);
        }
        
        // Buscar instrução padrão
        $instruction = $this->db->fetch(
            "SELECT value FROM settings WHERE key_name = 'default_prompt_instruction'"
        )['value'] ?? 'escreva um artigo de três mil palavras otimizado para SEO';
        
        $finalPrompt = rtrim($prompt) . ' ' . $instruction;
        
        // Fazer requisição para a API Groq
        $data = [
            "model" => "llama-3.3-70b-versatile",
            "temperature" => 1,
            "max_completion_tokens" => 8192,
            "messages" => [
                [
                    "role" => "user",
                    "content" => $finalPrompt
                ]
            ]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.groq.com/openai/v1/chat/completions');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $groqKey
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $responseTime = round((microtime(true) - $startTime) * 1000);
        curl_close($ch);
        
        // Log da requisição
        Auth::logApiUsage(
            $verification['key_id'],
            $verification['user_id'],
            '/api/v1/generate-content',
            'POST',
            $httpCode,
            $responseTime
        );
        
        if ($httpCode !== 200) {
            return ApiResponse::error('Erro na API Groq', $httpCode);
        }
        
        $result = json_decode($response, true);
        
        if (!isset($result['choices'][0]['message']['content'])) {
            return ApiResponse::error('Resposta inválida da API Groq', 500);
        }
        
        return ApiResponse::success([
            'content' => trim($result['choices'][0]['message']['content']),
            'usage' => [
                'monthly_usage' => $verification['monthly_usage'] + 1,
                'monthly_limit' => $verification['monthly_limit']
            ]
        ]);
    }
    
    public function takeScreenshot() {
        $apiKey = $this->input('api_key');
        $url = $this->input('url');
        
        if (!$apiKey || !$url) {
            return ApiResponse::error('Parâmetros obrigatórios: api_key, url', 400);
        }
        
        $startTime = microtime(true);
        $verification = Auth::verifyApiKey($apiKey);
        
        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }
        
        // Buscar chave do ApiFlash
        $apiflashKey = $this->db->fetch(
            "SELECT value FROM settings WHERE key_name = 'apiflash_key'"
        )['value'] ?? '';
        
        if (!$apiflashKey) {
            return ApiResponse::error('ApiFlash não configurado', 500);
        }
        
        // Fazer requisição para ApiFlash
        $params = http_build_query([
            'access_key' => $apiflashKey,
            'url' => $url,
            'format' => 'png',
            'width' => 1920,
            'height' => 1080,
            'full_page' => 'true'
        ]);
        
        $screenshotUrl = "https://api.apiflash.com/v1/urltoimage?" . $params;
        
        $responseTime = round((microtime(true) - $startTime) * 1000);
        
        // Log da requisição
        Auth::logApiUsage(
            $verification['key_id'],
            $verification['user_id'],
            '/api/v1/screenshot',
            'POST',
            200,
            $responseTime
        );
        
        return ApiResponse::success([
            'screenshot_url' => $screenshotUrl,
            'usage' => [
                'monthly_usage' => $verification['monthly_usage'] + 1,
                'monthly_limit' => $verification['monthly_limit']
            ]
        ]);
    }
    
    public function healthCheck() {
        return ApiResponse::success([
            'status' => 'ok',
            'timestamp' => date('c'),
            'version' => '1.0.0'
        ]);
    }

    public function validateKey() {
        $apiKey = $this->input('api_key') ?: $this->input('key');

        if (!$apiKey) {
            return ApiResponse::error('Chave de API não fornecida', 400);
        }

        $verification = Auth::verifyApiKey($apiKey);

        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }

        // Log da verificação
        Auth::logApiUsage(
            $verification['key_id'],
            $verification['user_id'],
            '/api/v1/validate',
            $_SERVER['REQUEST_METHOD'],
            200,
            0
        );

        return ApiResponse::success([
            'valid' => true,
            'status' => 'active',
            'user_id' => $verification['user_id'],
            'monthly_usage' => $verification['monthly_usage'],
            'monthly_limit' => $verification['monthly_limit'],
            'remaining' => $verification['monthly_limit'] ?
                          ($verification['monthly_limit'] - $verification['monthly_usage']) : null
        ]);
    }

    public function keyStatus() {
        $apiKey = $this->input('api_key') ?: $this->input('key');

        if (!$apiKey) {
            return ApiResponse::error('Chave de API não fornecida', 400);
        }

        $keyData = $this->db->fetch(
            "SELECT ak.*, u.status as user_status, s.status as subscription_status, s.ends_at, p.name as plan_name
             FROM api_keys ak
             JOIN users u ON ak.user_id = u.id
             JOIN subscriptions s ON ak.subscription_id = s.id
             JOIN plans p ON s.plan_id = p.id
             WHERE ak.api_key = :key",
            ['key' => $apiKey]
        );

        if (!$keyData) {
            return ApiResponse::error('Chave não encontrada', 404);
        }

        return ApiResponse::success([
            'key_status' => $keyData['status'],
            'user_status' => $keyData['user_status'],
            'subscription_status' => $keyData['subscription_status'],
            'plan_name' => $keyData['plan_name'],
            'expires_at' => $keyData['ends_at'],
            'monthly_usage' => $keyData['monthly_usage'],
            'monthly_limit' => $keyData['monthly_limit'],
            'last_used_at' => $keyData['last_used_at']
        ]);
    }

    public function getPlans() {
        $plans = $this->db->fetchAll("SELECT * FROM plans WHERE status = 'active' ORDER BY price ASC");

        return ApiResponse::success($plans);
    }

    public function generateToken() {
        $email = $this->input('email');
        $password = $this->input('password');

        if (!$email || !$password) {
            return ApiResponse::error('Email e senha são obrigatórios', 400);
        }

        // Verificar credenciais
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE email = :email AND status = 'active'",
            ['email' => $email]
        );

        if (!$user || !password_verify($password, $user['password'])) {
            return ApiResponse::error('Credenciais inválidas', 401);
        }

        // Gerar token JWT simples (para demonstração)
        $payload = [
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role'],
            'exp' => time() + 3600 // 1 hora
        ];

        $token = base64_encode(json_encode($payload));

        return ApiResponse::success([
            'token' => $token,
            'expires_in' => 3600,
            'user' => [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role']
            ]
        ]);
    }

    public function registerSite() {
        $apiKey = $this->input('api_key');
        $siteUrl = $this->input('site_url');
        $pluginVersion = $this->input('plugin_version', '1.0.0');

        if (!$apiKey || !$siteUrl) {
            return ApiResponse::error('API key e URL do site são obrigatórios', 400);
        }

        $verification = Auth::verifyApiKey($apiKey);

        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }

        // Buscar ou criar o site
        $site = $this->db->fetch(
            "SELECT * FROM client_sites WHERE user_id = :user_id AND site_url = :url",
            ['user_id' => $verification['user_id'], 'url' => $siteUrl]
        );

        if ($site) {
            // Atualizar site existente
            $this->db->update('client_sites', [
                'status' => 'connected',
                'plugin_version' => $pluginVersion,
                'last_connection' => date('Y-m-d H:i:s'),
                'api_key_id' => $verification['key_id']
            ], 'id = :id', ['id' => $site['id']]);
        } else {
            // Criar novo site
            $siteName = parse_url($siteUrl, PHP_URL_HOST);
            $this->db->insert('client_sites', [
                'user_id' => $verification['user_id'],
                'site_name' => $siteName,
                'site_url' => $siteUrl,
                'status' => 'connected',
                'plugin_version' => $pluginVersion,
                'last_connection' => date('Y-m-d H:i:s'),
                'api_key_id' => $verification['key_id']
            ]);
        }

        return ApiResponse::success([
            'message' => 'Site registrado com sucesso',
            'status' => 'connected'
        ]);
    }
}
