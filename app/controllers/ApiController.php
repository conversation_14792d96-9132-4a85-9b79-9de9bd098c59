<?php
/**
 * Controller da API
 * Sistema de Dashboard E1Copy AI
 */

class ApiController extends Controller {
    
    public function verifyKey() {
        $apiKey = $this->input('api_key') ?: $this->input('key');
        
        if (!$apiKey) {
            return ApiResponse::error('Chave de API não fornecida', 400);
        }
        
        $verification = Auth::verifyApiKey($apiKey);
        
        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }
        
        // Log da verificação
        Auth::logApiUsage(
            $verification['key_id'],
            $verification['user_id'],
            '/api/v1/verify-key',
            $_SERVER['REQUEST_METHOD'],
            200,
            0
        );
        
        return ApiResponse::success([
            'valid' => true,
            'user_id' => $verification['user_id'],
            'monthly_usage' => $verification['monthly_usage'],
            'monthly_limit' => $verification['monthly_limit'],
            'remaining' => $verification['monthly_limit'] ? 
                          ($verification['monthly_limit'] - $verification['monthly_usage']) : null
        ]);
    }
    
    public function clientStatus($apiKey) {
        $verification = Auth::verifyApiKey($apiKey);
        
        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }
        
        // Buscar informações do cliente
        $client = $this->db->fetch(
            "SELECT u.*, s.status as subscription_status, s.ends_at, p.name as plan_name
             FROM users u
             JOIN subscriptions s ON u.id = s.user_id
             JOIN plans p ON s.plan_id = p.id
             WHERE u.id = :user_id AND s.status = 'active'
             ORDER BY s.created_at DESC
             LIMIT 1",
            ['user_id' => $verification['user_id']]
        );
        
        if (!$client) {
            return ApiResponse::error('Cliente não encontrado', 404);
        }
        
        return ApiResponse::success([
            'client' => [
                'name' => $client['name'],
                'email' => $client['email'],
                'status' => $client['status'],
                'plan' => $client['plan_name'],
                'subscription_status' => $client['subscription_status'],
                'expires_at' => $client['ends_at']
            ],
            'usage' => [
                'monthly_usage' => $verification['monthly_usage'],
                'monthly_limit' => $verification['monthly_limit']
            ]
        ]);
    }
    
    public function suspendKey() {
        $apiKey = $this->input('api_key');
        $adminKey = $this->input('admin_key');
        
        // Verificar se é uma requisição administrativa válida
        // Por segurança, apenas admins podem suspender chaves
        if (!$adminKey || $adminKey !== config('app.admin_api_key')) {
            return ApiResponse::forbidden('Acesso negado');
        }
        
        if (!$apiKey) {
            return ApiResponse::error('Chave de API não fornecida', 400);
        }
        
        $result = $this->db->update(
            'api_keys',
            ['status' => 'suspended'],
            'api_key = :key',
            ['key' => $apiKey]
        );
        
        if ($result) {
            return ApiResponse::success(['message' => 'Chave suspensa com sucesso']);
        } else {
            return ApiResponse::error('Erro ao suspender chave', 500);
        }
    }
    
    public function activateKey() {
        $apiKey = $this->input('api_key');
        $adminKey = $this->input('admin_key');
        
        // Verificar se é uma requisição administrativa válida
        if (!$adminKey || $adminKey !== config('app.admin_api_key')) {
            return ApiResponse::forbidden('Acesso negado');
        }
        
        if (!$apiKey) {
            return ApiResponse::error('Chave de API não fornecida', 400);
        }
        
        $result = $this->db->update(
            'api_keys',
            ['status' => 'active'],
            'api_key = :key',
            ['key' => $apiKey]
        );
        
        if ($result) {
            return ApiResponse::success(['message' => 'Chave ativada com sucesso']);
        } else {
            return ApiResponse::error('Erro ao ativar chave', 500);
        }
    }
    
    public function usageStats($apiKey) {
        $verification = Auth::verifyApiKey($apiKey);
        
        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }
        
        // Estatísticas de uso
        $stats = $this->db->fetch(
            "SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_requests,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as week_requests,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as month_requests,
                AVG(response_time_ms) as avg_response_time
             FROM api_usage 
             WHERE api_key_id = :key_id",
            ['key_id' => $verification['key_id']]
        );
        
        return ApiResponse::success($stats);
    }
    
    public function generateContent() {
        $apiKey = $this->input('api_key');
        $prompt = $this->input('prompt');
        
        if (!$apiKey || !$prompt) {
            return ApiResponse::error('Parâmetros obrigatórios: api_key, prompt', 400);
        }
        
        $startTime = microtime(true);
        $verification = Auth::verifyApiKey($apiKey);
        
        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }
        
        // Buscar configurações da API Groq
        $groqKey = $this->db->fetch(
            "SELECT value FROM settings WHERE key_name = 'groq_api_key'"
        )['value'] ?? '';
        
        if (!$groqKey) {
            return ApiResponse::error('API Groq não configurada', 500);
        }
        
        // Buscar instrução padrão
        $instruction = $this->db->fetch(
            "SELECT value FROM settings WHERE key_name = 'default_prompt_instruction'"
        )['value'] ?? 'escreva um artigo de três mil palavras otimizado para SEO';
        
        $finalPrompt = rtrim($prompt) . ' ' . $instruction;
        
        // Fazer requisição para a API Groq
        $data = [
            "model" => "llama-3.3-70b-versatile",
            "temperature" => 1,
            "max_completion_tokens" => 8192,
            "messages" => [
                [
                    "role" => "user",
                    "content" => $finalPrompt
                ]
            ]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.groq.com/openai/v1/chat/completions');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $groqKey
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $responseTime = round((microtime(true) - $startTime) * 1000);
        curl_close($ch);
        
        // Log da requisição
        Auth::logApiUsage(
            $verification['key_id'],
            $verification['user_id'],
            '/api/v1/generate-content',
            'POST',
            $httpCode,
            $responseTime
        );
        
        if ($httpCode !== 200) {
            return ApiResponse::error('Erro na API Groq', $httpCode);
        }
        
        $result = json_decode($response, true);
        
        if (!isset($result['choices'][0]['message']['content'])) {
            return ApiResponse::error('Resposta inválida da API Groq', 500);
        }
        
        return ApiResponse::success([
            'content' => trim($result['choices'][0]['message']['content']),
            'usage' => [
                'monthly_usage' => $verification['monthly_usage'] + 1,
                'monthly_limit' => $verification['monthly_limit']
            ]
        ]);
    }
    
    public function takeScreenshot() {
        $apiKey = $this->input('api_key');
        $url = $this->input('url');
        
        if (!$apiKey || !$url) {
            return ApiResponse::error('Parâmetros obrigatórios: api_key, url', 400);
        }
        
        $startTime = microtime(true);
        $verification = Auth::verifyApiKey($apiKey);
        
        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }
        
        // Buscar chave do ApiFlash
        $apiflashKey = $this->db->fetch(
            "SELECT value FROM settings WHERE key_name = 'apiflash_key'"
        )['value'] ?? '';
        
        if (!$apiflashKey) {
            return ApiResponse::error('ApiFlash não configurado', 500);
        }
        
        // Fazer requisição para ApiFlash
        $params = http_build_query([
            'access_key' => $apiflashKey,
            'url' => $url,
            'format' => 'png',
            'width' => 1920,
            'height' => 1080,
            'full_page' => 'true'
        ]);
        
        $screenshotUrl = "https://api.apiflash.com/v1/urltoimage?" . $params;
        
        $responseTime = round((microtime(true) - $startTime) * 1000);
        
        // Log da requisição
        Auth::logApiUsage(
            $verification['key_id'],
            $verification['user_id'],
            '/api/v1/screenshot',
            'POST',
            200,
            $responseTime
        );
        
        return ApiResponse::success([
            'screenshot_url' => $screenshotUrl,
            'usage' => [
                'monthly_usage' => $verification['monthly_usage'] + 1,
                'monthly_limit' => $verification['monthly_limit']
            ]
        ]);
    }
    
    public function healthCheck() {
        return ApiResponse::success([
            'status' => 'ok',
            'timestamp' => date('c'),
            'version' => '1.0.0'
        ]);
    }

    public function validateKey() {
        $apiKey = $this->input('api_key') ?: $this->input('key');

        if (!$apiKey) {
            return ApiResponse::error('Chave de API não fornecida', 400);
        }

        $verification = Auth::verifyApiKey($apiKey);

        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }

        // Log da verificação
        Auth::logApiUsage(
            $verification['key_id'],
            $verification['user_id'],
            '/api/v1/validate',
            $_SERVER['REQUEST_METHOD'],
            200,
            0
        );

        return ApiResponse::success([
            'valid' => true,
            'status' => 'active',
            'user_id' => $verification['user_id'],
            'monthly_usage' => $verification['monthly_usage'],
            'monthly_limit' => $verification['monthly_limit'],
            'remaining' => $verification['monthly_limit'] ?
                          ($verification['monthly_limit'] - $verification['monthly_usage']) : null
        ]);
    }

    public function keyStatus() {
        $apiKey = $this->input('api_key') ?: $this->input('key');

        if (!$apiKey) {
            return ApiResponse::error('Chave de API não fornecida', 400);
        }

        $keyData = $this->db->fetch(
            "SELECT ak.*, u.status as user_status, s.status as subscription_status, s.ends_at, p.name as plan_name
             FROM api_keys ak
             JOIN users u ON ak.user_id = u.id
             JOIN subscriptions s ON ak.subscription_id = s.id
             JOIN plans p ON s.plan_id = p.id
             WHERE ak.api_key = :key",
            ['key' => $apiKey]
        );

        if (!$keyData) {
            return ApiResponse::error('Chave não encontrada', 404);
        }

        return ApiResponse::success([
            'key_status' => $keyData['status'],
            'user_status' => $keyData['user_status'],
            'subscription_status' => $keyData['subscription_status'],
            'plan_name' => $keyData['plan_name'],
            'expires_at' => $keyData['ends_at'],
            'monthly_usage' => $keyData['monthly_usage'],
            'monthly_limit' => $keyData['monthly_limit'],
            'last_used_at' => $keyData['last_used_at']
        ]);
    }

    public function getPlans() {
        $plans = $this->db->fetchAll("SELECT * FROM plans WHERE status = 'active' ORDER BY price ASC");

        return ApiResponse::success($plans);
    }

    public function generateToken() {
        $email = $this->input('email');
        $password = $this->input('password');

        if (!$email || !$password) {
            return ApiResponse::error('Email e senha são obrigatórios', 400);
        }

        // Verificar credenciais
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE email = :email AND status = 'active'",
            ['email' => $email]
        );

        if (!$user || !password_verify($password, $user['password'])) {
            return ApiResponse::error('Credenciais inválidas', 401);
        }

        // Gerar token JWT simples (para demonstração)
        $payload = [
            'user_id' => $user['id'],
            'email' => $user['email'],
            'role' => $user['role'],
            'exp' => time() + 3600 // 1 hora
        ];

        $token = base64_encode(json_encode($payload));

        return ApiResponse::success([
            'token' => $token,
            'expires_in' => 3600,
            'user' => [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role']
            ]
        ]);
    }

    public function registerSite() {
        $apiKey = $this->input('api_key');
        $siteUrl = $this->input('site_url');
        $pluginVersion = $this->input('plugin_version', '1.0.0');

        if (!$apiKey || !$siteUrl) {
            return ApiResponse::error('API key e URL do site são obrigatórios', 400);
        }

        $verification = Auth::verifyApiKey($apiKey);

        if (!$verification['valid']) {
            return ApiResponse::error($verification['reason'], 401);
        }

        // Buscar ou criar o site
        $site = $this->db->fetch(
            "SELECT * FROM client_sites WHERE user_id = :user_id AND site_url = :url",
            ['user_id' => $verification['user_id'], 'url' => $siteUrl]
        );

        if ($site) {
            // Atualizar site existente
            $this->db->update('client_sites', [
                'status' => 'connected',
                'plugin_version' => $pluginVersion,
                'last_connection' => date('Y-m-d H:i:s'),
                'api_key_id' => $verification['key_id']
            ], 'id = :id', ['id' => $site['id']]);
        } else {
            // Criar novo site
            $siteName = parse_url($siteUrl, PHP_URL_HOST);
            $this->db->insert('client_sites', [
                'user_id' => $verification['user_id'],
                'site_name' => $siteName,
                'site_url' => $siteUrl,
                'status' => 'connected',
                'plugin_version' => $pluginVersion,
                'last_connection' => date('Y-m-d H:i:s'),
                'api_key_id' => $verification['key_id']
            ]);
        }

        return ApiResponse::success([
            'message' => 'Site registrado com sucesso',
            'status' => 'connected'
        ]);
    }

    /**
     * Lista posts pendentes de todos os sites conectados
     * Endpoint: GET /api/v1/pending-posts
     */
    public function getPendingPosts() {
        // Buscar todos os sites conectados com suas chaves de API
        $sites = $this->db->fetchAll("
            SELECT cs.*, u.name as user_name, u.email, ak.api_key
            FROM client_sites cs
            LEFT JOIN users u ON cs.user_id = u.id
            LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
            WHERE cs.status = 'connected'
            AND u.status = 'active'
            AND ak.status = 'active'
            ORDER BY cs.site_name
        ");

        $allPosts = [];
        $siteStats = [];

        foreach ($sites as $site) {
            $siteData = [
                'site_id' => $site['id'],
                'site_name' => $site['site_name'],
                'site_url' => $site['site_url'],
                'user_name' => $site['user_name'],
                'user_email' => $site['email'],
                'posts' => [],
                'total_posts' => 0,
                'status' => 'error',
                'error_message' => null
            ];

            try {
                // Fazer requisição para o endpoint do plugin WordPress
                $apiUrl = rtrim($site['site_url'], '/') . '/wp-json/e1copy-ai/v1/products?processed=0';

                $response = $this->makeHttpRequest($apiUrl, [
                    'X-E1Copy-API-Key: ' . $site['api_key']
                ]);

                if ($response['success']) {
                    $posts = [];

                    // Tentar diferentes estruturas de resposta
                    if (isset($response['data']['posts'])) {
                        // Estrutura: { "data": { "posts": [...] } }
                        $posts = $response['data']['posts'];
                    } elseif (isset($response['data']) && is_array($response['data'])) {
                        // Estrutura: { "data": [...] } (array direto)
                        $posts = $response['data'];
                    } elseif (is_array($response['data'])) {
                        // Estrutura: [...] (array direto na raiz)
                        $posts = $response['data'];
                    }

                    // Garantir que $posts é um array
                    if (!is_array($posts)) {
                        $posts = [];
                    }

                    // Adicionar informações do site a cada post
                    foreach ($posts as &$post) {
                        $post['site_info'] = [
                            'site_id' => $site['id'],
                            'site_name' => $site['site_name'],
                            'site_url' => $site['site_url'],
                            'user_name' => $site['user_name'],
                            'user_email' => $site['email']
                        ];
                    }

                    $siteData['posts'] = $posts;
                    $siteData['total_posts'] = count($posts);
                    $siteData['status'] = 'success';

                    // Adicionar posts ao array geral
                    $allPosts = array_merge($allPosts, $posts);
                } else {
                    $siteData['error_message'] = $response['error'] ?? 'Erro desconhecido ao acessar API do site';
                }
            } catch (Exception $e) {
                $siteData['error_message'] = 'Erro de conexão: ' . $e->getMessage();
            }

            $siteStats[] = $siteData;
        }

        // Organizar posts por site/cliente - INCLUIR TODOS OS SITES
        $postsBySite = [];
        $postsByClient = [];

        foreach ($siteStats as $siteData) {
            // SEMPRE incluir o site, mesmo sem posts
            $postsBySite[] = [
                'site_info' => [
                    'site_id' => $siteData['site_id'],
                    'site_name' => $siteData['site_name'],
                    'site_url' => $siteData['site_url'],
                    'user_name' => $siteData['user_name'],
                    'user_email' => $siteData['user_email']
                ],
                'posts' => $siteData['posts'],
                'total_posts' => $siteData['total_posts'],
                'status' => $siteData['status']
            ];

            // Posts por cliente (agrupados por email) - SEMPRE incluir
            $clientKey = $siteData['user_email'];
            if (!isset($postsByClient[$clientKey])) {
                $postsByClient[$clientKey] = [
                    'client_info' => [
                        'user_name' => $siteData['user_name'],
                        'user_email' => $siteData['user_email']
                    ],
                    'sites' => [],
                    'total_posts' => 0,
                    'posts' => []
                ];
            }

            $postsByClient[$clientKey]['sites'][] = [
                'site_id' => $siteData['site_id'],
                'site_name' => $siteData['site_name'],
                'site_url' => $siteData['site_url'],
                'posts_count' => $siteData['total_posts'],
                'status' => $siteData['status']
            ];

            $postsByClient[$clientKey]['total_posts'] += $siteData['total_posts'];
            if ($siteData['total_posts'] > 0) {
                $postsByClient[$clientKey]['posts'] = array_merge(
                    $postsByClient[$clientKey]['posts'],
                    $siteData['posts']
                );
            }
        }

        // Calcular estatísticas corretas
        $sitesWithPosts = array_filter($siteStats, function($s) { return $s['total_posts'] > 0; });
        $sitesConnected = array_filter($siteStats, function($s) { return $s['status'] === 'success'; });

        return ApiResponse::success([
            'summary' => [
                'total_sites' => count($sites),
                'total_posts' => count($allPosts),
                'active_sites' => count($sitesConnected), // Sites que responderam com sucesso
                'sites_with_posts' => count($sitesWithPosts), // Sites que têm posts
                'active_clients' => count($postsByClient)
            ],
            'posts_by_site' => $postsBySite,
            'posts_by_client' => array_values($postsByClient),
            'all_posts' => $allPosts, // Manter compatibilidade
            'sites_stats' => $siteStats, // Dados detalhados de cada site
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Endpoint específico para N8N - retorna posts organizados por cliente
     * Endpoint: GET /api/v1/pending-posts/by-client
     */
    public function getPendingPostsByClient()
    {
        try {
            $data = $this->getPendingPosts();
            $responseData = $data->getData();

            // Retornar apenas a estrutura organizada por cliente
            return ApiResponse::success([
                'clients' => $responseData['posts_by_client'] ?? [],
                'summary' => $responseData['summary'] ?? [],
                'timestamp' => $responseData['timestamp'] ?? date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Erro ao buscar posts por cliente: " . $e->getMessage());
            return ApiResponse::error('Erro interno do servidor', 500);
        }
    }

    /**
     * Endpoint específico para N8N - retorna posts organizados por site
     * Endpoint: GET /api/v1/pending-posts/by-site
     */
    public function getPendingPostsBySite()
    {
        try {
            $data = $this->getPendingPosts();
            $responseData = $data->getData();

            // Retornar apenas a estrutura organizada por site
            return ApiResponse::success([
                'sites' => $responseData['posts_by_site'] ?? [],
                'summary' => $responseData['summary'] ?? [],
                'timestamp' => $responseData['timestamp'] ?? date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Erro ao buscar posts por site: " . $e->getMessage());
            return ApiResponse::error('Erro interno do servidor', 500);
        }
    }

    /**
     * Faz requisição HTTP para APIs externas
     */
    private function makeHttpRequest($url, $headers = [], $timeout = 30) {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_USERAGENT => 'E1Copy-Dashboard/1.0'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'error' => 'Erro cURL: ' . $error,
                'http_code' => 0
            ];
        }

        $data = json_decode($response, true);

        return [
            'success' => $httpCode >= 200 && $httpCode < 300,
            'data' => $data,
            'http_code' => $httpCode,
            'error' => $httpCode >= 400 ? 'HTTP Error ' . $httpCode : null
        ];
    }
}
