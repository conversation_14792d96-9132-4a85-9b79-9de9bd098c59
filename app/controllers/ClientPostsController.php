<?php

class ClientPostsController extends BaseController {
    
    /**
     * Lista os posts do cliente
     */
    public function index() {
        $userId = $_SESSION['user_id'];
        $search = $this->input('search', '');
        $status = $this->input('status', '');
        $postType = $this->input('post_type', '');
        $page = max(1, (int)$this->input('page', 1));
        $perPage = 10;
        $offset = ($page - 1) * $perPage;
        
        // Construir condições da query
        $conditions = ['cp.user_id = :user_id'];
        $params = ['user_id' => $userId];
        
        if ($search) {
            $conditions[] = "(cp.title LIKE :search OR cp.content LIKE :search OR cp.keywords LIKE :search)";
            $params['search'] = "%{$search}%";
        }
        
        if ($status) {
            $conditions[] = "cp.status = :status";
            $params['status'] = $status;
        }
        
        if ($postType) {
            $conditions[] = "cp.post_type = :post_type";
            $params['post_type'] = $postType;
        }
        
        $whereClause = implode(' AND ', $conditions);
        
        // Buscar posts
        $posts = $this->db->fetchAll("
            SELECT 
                cp.*,
                cs.site_name,
                cs.site_url
            FROM client_posts cp
            LEFT JOIN client_sites cs ON cp.site_id = cs.id
            WHERE {$whereClause}
            ORDER BY cp.created_at DESC
            LIMIT {$perPage} OFFSET {$offset}
        ", $params);
        
        // Contar total
        $totalPosts = $this->db->fetch("
            SELECT COUNT(*) as total
            FROM client_posts cp
            WHERE {$whereClause}
        ", $params)['total'];
        
        // Buscar sites do usuário
        $sites = $this->db->fetchAll("
            SELECT cs.id, cs.site_name, cs.site_url, cs.status
            FROM client_sites cs
            WHERE cs.user_id = :user_id AND cs.status = 'connected'
            ORDER BY cs.site_name
        ", ['user_id' => $userId]);
        
        // Estatísticas
        $stats = $this->db->fetch("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as drafts,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
            FROM client_posts
            WHERE user_id = :user_id
        ", ['user_id' => $userId]);
        
        $totalPages = ceil($totalPosts / $perPage);
        
        $this->view('client.posts.index', [
            'posts' => $posts,
            'sites' => $sites,
            'stats' => $stats,
            'search' => $search,
            'status' => $status,
            'postType' => $postType,
            'page' => $page,
            'totalPages' => $totalPages,
            'totalPosts' => $totalPosts
        ]);
    }
    
    /**
     * Mostra formulário para criar novo post
     */
    public function create() {
        $userId = $_SESSION['user_id'];
        
        // Buscar sites do usuário
        $sites = $this->db->fetchAll("
            SELECT cs.id, cs.site_name, cs.site_url, cs.status
            FROM client_sites cs
            WHERE cs.user_id = :user_id AND cs.status = 'connected'
            ORDER BY cs.site_name
        ", ['user_id' => $userId]);
        
        if (empty($sites)) {
            $this->redirect('/client/sites?error=' . urlencode('Você precisa ter pelo menos um site conectado para criar posts.'));
            return;
        }
        
        $this->view('client.posts.create', [
            'sites' => $sites
        ]);
    }
    
    /**
     * Mostra formulário específico baseado no tipo de post
     */
    public function createForm() {
        $postType = $this->input('type');
        $siteId = $this->input('site_id');
        $userId = $_SESSION['user_id'];
        
        if (!in_array($postType, ['article', 'product_review'])) {
            $this->redirect('/client/posts/create?error=' . urlencode('Tipo de post inválido.'));
            return;
        }
        
        // Verificar se o site pertence ao usuário
        $site = $this->db->fetch("
            SELECT * FROM client_sites 
            WHERE id = :site_id AND user_id = :user_id AND status = 'connected'
        ", ['site_id' => $siteId, 'user_id' => $userId]);
        
        if (!$site) {
            $this->redirect('/client/posts/create?error=' . urlencode('Site não encontrado ou não conectado.'));
            return;
        }
        
        $this->view('client.posts.form', [
            'postType' => $postType,
            'site' => $site
        ]);
    }
    
    /**
     * Salva o post
     */
    public function store() {
        try {
            $userId = $_SESSION['user_id'];
            $postType = $this->input('post_type');
            $siteId = $this->input('site_id');
            $action = $this->input('action', 'save_draft'); // save_draft ou publish
            
            // Validações básicas
            if (!in_array($postType, ['article', 'product_review'])) {
                throw new Exception('Tipo de post inválido.');
            }
            
            // Verificar se o site pertence ao usuário
            $site = $this->db->fetch("
                SELECT * FROM client_sites 
                WHERE id = :site_id AND user_id = :user_id AND status = 'connected'
            ", ['site_id' => $siteId, 'user_id' => $userId]);
            
            if (!$site) {
                throw new Exception('Site não encontrado ou não conectado.');
            }
            
            // Dados comuns
            $data = [
                'site_id' => $siteId,
                'user_id' => $userId,
                'post_type' => $postType,
                'title' => $this->input('title'),
                'content' => $this->input('content'),
                'excerpt' => $this->input('excerpt'),
                'keywords' => $this->input('keywords'),
                'category' => $this->input('category'),
                'tags' => $this->input('tags'),
                'slug' => $this->generateSlug($this->input('title')),
                'status' => $action === 'publish' ? 'pending' : 'draft'
            ];
            
            // Validações específicas
            if (empty($data['title'])) {
                throw new Exception('Título é obrigatório.');
            }
            
            if (empty($data['content'])) {
                throw new Exception('Conteúdo é obrigatório.');
            }
            
            // Campos específicos para product review
            if ($postType === 'product_review') {
                $data['product_name'] = $this->input('product_name');
                $data['product_url'] = $this->input('product_url');
                $data['product_price'] = $this->input('product_price') ? (float)$this->input('product_price') : null;
                $data['product_rating'] = $this->input('product_rating') ? (float)$this->input('product_rating') : null;
                $data['product_pros'] = $this->input('product_pros');
                $data['product_cons'] = $this->input('product_cons');
                $data['affiliate_link'] = $this->input('affiliate_link');
                
                if (empty($data['product_name'])) {
                    throw new Exception('Nome do produto é obrigatório para reviews.');
                }
            }
            
            // Inserir no banco
            $postId = $this->db->insert('client_posts', $data);
            
            $message = $action === 'publish' 
                ? 'Post criado e enviado para publicação!' 
                : 'Rascunho salvo com sucesso!';
                
            $this->redirect('/client/posts?success=' . urlencode($message));
            
        } catch (Exception $e) {
            $this->redirect('/client/posts/create?error=' . urlencode($e->getMessage()));
        }
    }
    
    /**
     * Mostra detalhes do post
     */
    public function show($id) {
        $userId = $_SESSION['user_id'];
        
        $post = $this->db->fetch("
            SELECT 
                cp.*,
                cs.site_name,
                cs.site_url
            FROM client_posts cp
            LEFT JOIN client_sites cs ON cp.site_id = cs.id
            WHERE cp.id = :id AND cp.user_id = :user_id
        ", ['id' => $id, 'user_id' => $userId]);
        
        if (!$post) {
            $this->redirect('/client/posts?error=' . urlencode('Post não encontrado.'));
            return;
        }
        
        $this->view('client.posts.show', [
            'post' => $post
        ]);
    }
    
    /**
     * Edita o post
     */
    public function edit($id) {
        $userId = $_SESSION['user_id'];
        
        $post = $this->db->fetch("
            SELECT 
                cp.*,
                cs.site_name,
                cs.site_url
            FROM client_posts cp
            LEFT JOIN client_sites cs ON cp.site_id = cs.id
            WHERE cp.id = :id AND cp.user_id = :user_id
        ", ['id' => $id, 'user_id' => $userId]);
        
        if (!$post) {
            $this->redirect('/client/posts?error=' . urlencode('Post não encontrado.'));
            return;
        }
        
        // Não permitir edição de posts já publicados
        if (in_array($post['status'], ['completed', 'published'])) {
            $this->redirect('/client/posts?error=' . urlencode('Posts publicados não podem ser editados.'));
            return;
        }
        
        // Buscar sites do usuário
        $sites = $this->db->fetchAll("
            SELECT cs.id, cs.site_name, cs.site_url, cs.status
            FROM client_sites cs
            WHERE cs.user_id = :user_id AND cs.status = 'connected'
            ORDER BY cs.site_name
        ", ['user_id' => $userId]);
        
        $this->view('client.posts.edit', [
            'post' => $post,
            'sites' => $sites
        ]);
    }
    
    /**
     * Atualiza o post
     */
    public function update($id) {
        try {
            $userId = $_SESSION['user_id'];
            $action = $this->input('action', 'save_draft');
            
            // Verificar se o post existe e pertence ao usuário
            $post = $this->db->fetch("
                SELECT * FROM client_posts 
                WHERE id = :id AND user_id = :user_id
            ", ['id' => $id, 'user_id' => $userId]);
            
            if (!$post) {
                throw new Exception('Post não encontrado.');
            }
            
            // Não permitir edição de posts já publicados
            if (in_array($post['status'], ['completed', 'published'])) {
                throw new Exception('Posts publicados não podem ser editados.');
            }
            
            // Dados para atualização
            $data = [
                'title' => $this->input('title'),
                'content' => $this->input('content'),
                'excerpt' => $this->input('excerpt'),
                'keywords' => $this->input('keywords'),
                'category' => $this->input('category'),
                'tags' => $this->input('tags'),
                'slug' => $this->generateSlug($this->input('title')),
                'status' => $action === 'publish' ? 'pending' : 'draft'
            ];
            
            // Validações
            if (empty($data['title'])) {
                throw new Exception('Título é obrigatório.');
            }
            
            if (empty($data['content'])) {
                throw new Exception('Conteúdo é obrigatório.');
            }
            
            // Campos específicos para product review
            if ($post['post_type'] === 'product_review') {
                $data['product_name'] = $this->input('product_name');
                $data['product_url'] = $this->input('product_url');
                $data['product_price'] = $this->input('product_price') ? (float)$this->input('product_price') : null;
                $data['product_rating'] = $this->input('product_rating') ? (float)$this->input('product_rating') : null;
                $data['product_pros'] = $this->input('product_pros');
                $data['product_cons'] = $this->input('product_cons');
                $data['affiliate_link'] = $this->input('affiliate_link');
                
                if (empty($data['product_name'])) {
                    throw new Exception('Nome do produto é obrigatório para reviews.');
                }
            }
            
            // Atualizar no banco
            $this->db->update('client_posts', $data, ['id' => $id]);
            
            $message = $action === 'publish' 
                ? 'Post atualizado e enviado para publicação!' 
                : 'Rascunho atualizado com sucesso!';
                
            $this->redirect('/client/posts?success=' . urlencode($message));
            
        } catch (Exception $e) {
            $this->redirect('/client/posts/' . $id . '/edit?error=' . urlencode($e->getMessage()));
        }
    }
    
    /**
     * Exclui o post
     */
    public function delete($id) {
        try {
            $userId = $_SESSION['user_id'];
            
            // Verificar se o post existe e pertence ao usuário
            $post = $this->db->fetch("
                SELECT * FROM client_posts 
                WHERE id = :id AND user_id = :user_id
            ", ['id' => $id, 'user_id' => $userId]);
            
            if (!$post) {
                throw new Exception('Post não encontrado.');
            }
            
            // Não permitir exclusão de posts já publicados
            if (in_array($post['status'], ['completed', 'published'])) {
                throw new Exception('Posts publicados não podem ser excluídos.');
            }
            
            // Excluir
            $this->db->delete('client_posts', ['id' => $id]);
            
            $this->redirect('/client/posts?success=' . urlencode('Post excluído com sucesso!'));
            
        } catch (Exception $e) {
            $this->redirect('/client/posts?error=' . urlencode($e->getMessage()));
        }
    }
    
    /**
     * Publica o post (muda status para pending)
     */
    public function publish($id) {
        try {
            $userId = $_SESSION['user_id'];

            // Verificar se o post existe e pertence ao usuário
            $post = $this->db->fetch("
                SELECT * FROM client_posts
                WHERE id = :id AND user_id = :user_id
            ", ['id' => $id, 'user_id' => $userId]);

            if (!$post) {
                throw new Exception('Post não encontrado.');
            }

            // Só pode publicar rascunhos ou posts que falharam
            if (!in_array($post['status'], ['draft', 'failed'])) {
                throw new Exception('Apenas rascunhos ou posts que falharam podem ser publicados.');
            }

            // Atualizar status
            $this->db->update('client_posts', ['status' => 'pending'], ['id' => $id]);

            $this->redirect('/client/posts?success=' . urlencode('Post enviado para publicação!'));

        } catch (Exception $e) {
            $this->redirect('/client/posts?error=' . urlencode($e->getMessage()));
        }
    }

    /**
     * Gera slug a partir do título
     */
    private function generateSlug($title) {
        $slug = strtolower($title);
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        $slug = trim($slug, '-');
        return $slug;
    }
}
