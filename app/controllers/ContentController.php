<?php
/**
 * Controller de Geração de Conteúdo
 * Sistema de Dashboard E1Copy AI
 */

class ContentController extends Controller {
    private $queueService;
    
    public function __construct() {
        parent::__construct();
        $this->queueService = new ContentQueueService();
    }
    
    /**
     * Adicionar post à fila de geração
     */
    public function addToQueue() {
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }
        
        // Validar dados
        $validation = $this->validate([
            'site_url' => 'required|url',
            'topic' => 'required|min:5|max:500'
        ]);
        
        if ($validation !== true) {
            return $this->json(['success' => false, 'message' => 'Dados inválidos', 'errors' => $validation], 422);
        }
        
        try {
            $userId = $this->input('user_id');
            $siteUrl = $this->input('site_url');
            $topic = $this->input('topic');
            
            // Verificar se o usuário tem permissão para este site
            if (!Auth::isAdmin() && !$this->userOwnsSite($userId, $siteUrl)) {
                return $this->json(['success' => false, 'message' => 'Acesso negado'], 403);
            }
            
            $options = [
                'site_id' => $this->input('site_id'),
                'site_name' => $this->input('site_name'),
                'template_id' => $this->input('template_id'),
                'keywords' => $this->input('keywords', []),
                'custom_variables' => $this->input('custom_variables', []),
                'priority' => $this->input('priority', 0),
                'scheduled_for' => $this->input('scheduled_for')
            ];
            
            $queueId = $this->queueService->addToQueue($userId, $siteUrl, $topic, $options);
            
            return $this->json([
                'success' => true,
                'message' => 'Post adicionado à fila com sucesso!',
                'queue_id' => $queueId
            ]);
            
        } catch (Exception $e) {
            return $this->json(['success' => false, 'message' => 'Erro: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Processar fila manualmente
     */
    public function processQueue() {
        if (!Auth::isAdmin()) {
            return $this->json(['success' => false, 'message' => 'Acesso negado'], 403);
        }
        
        try {
            $maxItems = $this->input('max_items', 5);
            $result = $this->queueService->processAll($maxItems);
            
            return $this->json([
                'success' => true,
                'message' => "Processados {$result['processed']} itens da fila",
                'data' => $result
            ]);
            
        } catch (Exception $e) {
            return $this->json(['success' => false, 'message' => 'Erro: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Gerar post imediatamente (sem fila)
     */
    public function generateNow() {
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }
        
        // Validar dados
        $validation = $this->validate([
            'site_url' => 'required|url',
            'topic' => 'required|min:5|max:500',
            'template_id' => 'required|numeric'
        ]);
        
        if ($validation !== true) {
            return $this->json(['success' => false, 'message' => 'Dados inválidos', 'errors' => $validation], 422);
        }
        
        try {
            $userId = $this->input('user_id');
            $siteUrl = $this->input('site_url');
            
            // Verificar permissão
            if (!Auth::isAdmin() && !$this->userOwnsSite($userId, $siteUrl)) {
                return $this->json(['success' => false, 'message' => 'Acesso negado'], 403);
            }
            
            // Adicionar à fila com prioridade alta
            $options = [
                'site_id' => $this->input('site_id'),
                'site_name' => $this->input('site_name'),
                'template_id' => $this->input('template_id'),
                'keywords' => $this->input('keywords', []),
                'custom_variables' => $this->input('custom_variables', []),
                'priority' => 999 // Prioridade máxima
            ];
            
            $queueId = $this->queueService->addToQueue($userId, $siteUrl, $this->input('topic'), $options);
            
            // Processar imediatamente
            $result = $this->queueService->processItem($queueId);
            
            return $this->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'Post gerado com sucesso!' : 'Erro na geração: ' . $result['error'],
                'data' => $result
            ]);
            
        } catch (Exception $e) {
            return $this->json(['success' => false, 'message' => 'Erro: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Publicar post no WordPress
     */
    public function publishPost() {
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }
        
        $postId = $this->input('post_id');
        
        if (!$postId) {
            return $this->json(['success' => false, 'message' => 'ID do post não fornecido'], 400);
        }
        
        try {
            // Verificar se o post existe e se o usuário tem permissão
            $post = $this->db->fetch("SELECT * FROM generated_posts WHERE id = :id", ['id' => $postId]);
            
            if (!$post) {
                return $this->json(['success' => false, 'message' => 'Post não encontrado'], 404);
            }
            
            if (!Auth::isAdmin() && $post['user_id'] != Auth::user()['id']) {
                return $this->json(['success' => false, 'message' => 'Acesso negado'], 403);
            }
            
            $result = $this->queueService->publishToWordPress($postId);
            
            return $this->json([
                'success' => true,
                'message' => 'Post publicado com sucesso!',
                'data' => $result
            ]);
            
        } catch (Exception $e) {
            return $this->json(['success' => false, 'message' => 'Erro: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Listar posts gerados
     */
    public function listPosts() {
        $userId = $this->input('user_id');
        $siteUrl = $this->input('site_url');
        $status = $this->input('status');
        $limit = $this->input('limit', 20);
        $offset = $this->input('offset', 0);
        
        $conditions = [];
        $params = [];
        
        // Filtros
        if (!Auth::isAdmin()) {
            $conditions[] = "user_id = :current_user_id";
            $params['current_user_id'] = Auth::user()['id'];
        } elseif ($userId) {
            $conditions[] = "user_id = :user_id";
            $params['user_id'] = $userId;
        }
        
        if ($siteUrl) {
            $conditions[] = "site_url = :site_url";
            $params['site_url'] = $siteUrl;
        }
        
        if ($status) {
            $conditions[] = "generation_status = :status";
            $params['status'] = $status;
        }
        
        $whereClause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
        
        // Buscar posts
        $posts = $this->db->fetchAll("
            SELECT gp.*, u.name as user_name, u.email as user_email
            FROM generated_posts gp
            JOIN users u ON gp.user_id = u.id
            {$whereClause}
            ORDER BY gp.created_at DESC
            LIMIT :limit OFFSET :offset
        ", array_merge($params, ['limit' => $limit, 'offset' => $offset]));
        
        // Contar total
        $total = $this->db->fetch("
            SELECT COUNT(*) as total
            FROM generated_posts gp
            JOIN users u ON gp.user_id = u.id
            {$whereClause}
        ", $params)['total'];
        
        return $this->json([
            'success' => true,
            'data' => [
                'posts' => $posts,
                'total' => $total,
                'limit' => $limit,
                'offset' => $offset
            ]
        ]);
    }
    
    /**
     * Obter estatísticas da fila
     */
    public function getQueueStats() {
        if (!Auth::isAdmin()) {
            return $this->json(['success' => false, 'message' => 'Acesso negado'], 403);
        }
        
        $stats = $this->queueService->getQueueStats();
        
        return $this->json([
            'success' => true,
            'data' => $stats
        ]);
    }
    
    /**
     * Cancelar item da fila
     */
    public function cancelQueueItem() {
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }
        
        $queueId = $this->input('queue_id');
        
        if (!$queueId) {
            return $this->json(['success' => false, 'message' => 'ID da fila não fornecido'], 400);
        }
        
        try {
            // Verificar permissão
            if (!Auth::isAdmin()) {
                $item = $this->db->fetch("SELECT user_id FROM content_queue WHERE id = :id", ['id' => $queueId]);
                if (!$item || $item['user_id'] != Auth::user()['id']) {
                    return $this->json(['success' => false, 'message' => 'Acesso negado'], 403);
                }
            }
            
            $result = $this->queueService->cancelItem($queueId);
            
            return $this->json([
                'success' => true,
                'message' => 'Item cancelado com sucesso!'
            ]);
            
        } catch (Exception $e) {
            return $this->json(['success' => false, 'message' => 'Erro: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Reprocessar item falhado
     */
    public function retryQueueItem() {
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }
        
        $queueId = $this->input('queue_id');
        
        if (!$queueId) {
            return $this->json(['success' => false, 'message' => 'ID da fila não fornecido'], 400);
        }
        
        try {
            // Verificar permissão
            if (!Auth::isAdmin()) {
                $item = $this->db->fetch("SELECT user_id FROM content_queue WHERE id = :id", ['id' => $queueId]);
                if (!$item || $item['user_id'] != Auth::user()['id']) {
                    return $this->json(['success' => false, 'message' => 'Acesso negado'], 403);
                }
            }
            
            $result = $this->queueService->retryItem($queueId);
            
            return $this->json([
                'success' => true,
                'message' => 'Item reprocessado com sucesso!'
            ]);
            
        } catch (Exception $e) {
            return $this->json(['success' => false, 'message' => 'Erro: ' . $e->getMessage()], 500);
        }
    }
    
    // Métodos auxiliares
    
    private function userOwnsSite($userId, $siteUrl) {
        $site = $this->db->fetch(
            "SELECT id FROM client_sites WHERE user_id = :user_id AND site_url = :site_url",
            ['user_id' => $userId, 'site_url' => $siteUrl]
        );
        
        return $site !== false;
    }
}
