<?php
/**
 * Controller da Área Administrativa
 * Sistema de Dashboard E1Copy AI
 */

class AdminController extends Controller {
    
    public function showLogin() {
        // Se já estiver logado como admin, redirecionar
        if (Auth::check() && Auth::isAdmin()) {
            redirect(url('/admin/dashboard'));
        }
        
        $this->view('admin.login');
    }
    
    public function login() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return $this->withInput()->redirect(url('/admin/login'));
        }
        
        // Validar dados
        $validation = $this->validate([
            'email' => 'required|email',
            'password' => 'required|min:6'
        ]);
        
        if ($validation !== true) {
            return $this->withErrors($validation)->withInput()->redirect(url('/admin/login'));
        }
        
        $email = $this->input('email');
        $password = $this->input('password');
        
        if (Auth::attempt($email, $password)) {
            // Verificar se é admin
            if (!Auth::isAdmin()) {
                Auth::logout();
                $this->flash('error', 'Acesso negado. Apenas administradores podem acessar esta área.');
                return redirect(url('/admin/login'));
            }
            
            // Login bem-sucedido
            $intendedUrl = $_SESSION['intended_url'] ?? null;
            unset($_SESSION['intended_url']);
            
            redirect($intendedUrl ?: url('/admin/dashboard'));
        } else {
            $this->flash('error', 'Email ou senha incorretos');
            return $this->withInput()->redirect(url('/admin/login'));
        }
    }
    
    public function dashboard() {
        // Estatísticas gerais
        $stats = [
            'total_users' => $this->db->fetch("SELECT COUNT(*) as count FROM users WHERE role = 'client'")['count'],
            'active_subscriptions' => $this->db->fetch("SELECT COUNT(*) as count FROM subscriptions WHERE status = 'active'")['count'],
            'total_api_keys' => $this->db->fetch("SELECT COUNT(*) as count FROM api_keys WHERE status = 'active'")['count'],
            'today_requests' => $this->db->fetch("SELECT COUNT(*) as count FROM api_usage WHERE DATE(created_at) = CURDATE()")['count']
        ];
        
        // Usuários recentes
        $recentUsers = $this->db->fetchAll(
            "SELECT * FROM users WHERE role = 'client' ORDER BY created_at DESC LIMIT 5"
        );
        
        // Atividade recente da API
        $recentActivity = $this->db->fetchAll(
            "SELECT au.*, u.name, u.email 
             FROM api_usage au
             JOIN users u ON au.user_id = u.id
             ORDER BY au.created_at DESC 
             LIMIT 10"
        );
        
        $this->view('admin.dashboard', [
            'stats' => $stats,
            'recentUsers' => $recentUsers,
            'recentActivity' => $recentActivity
        ]);
    }
    
    public function clients() {
        $page = (int) $this->input('page', 1);
        $search = $this->input('search', '');
        $status = $this->input('status', '');
        
        $conditions = "role = 'client'";
        $params = [];
        
        if ($search) {
            $conditions .= " AND (name LIKE :search OR email LIKE :search)";
            $params['search'] = "%{$search}%";
        }
        
        if ($status) {
            $conditions .= " AND status = :status";
            $params['status'] = $status;
        }
        
        $clients = $this->db->paginate($page, 20, $conditions, $params, 'users');
        
        // Buscar informações de assinatura para cada cliente
        foreach ($clients['data'] as &$client) {
            $subscription = $this->db->fetch(
                "SELECT s.*, p.name as plan_name 
                 FROM subscriptions s
                 JOIN plans p ON s.plan_id = p.id
                 WHERE s.user_id = :user_id AND s.status = 'active'
                 ORDER BY s.created_at DESC
                 LIMIT 1",
                ['user_id' => $client['id']]
            );
            $client['subscription'] = $subscription;
        }
        
        $this->view('admin.clients', [
            'clients' => $clients,
            'search' => $search,
            'status' => $status
        ]);
    }
    
    public function showClient($id) {
        $client = $this->db->fetch("SELECT * FROM users WHERE id = :id AND role = 'client'", ['id' => $id]);
        
        if (!$client) {
            $this->flash('error', 'Cliente não encontrado');
            return redirect(url('/admin/clients'));
        }
        
        // Buscar assinaturas
        $subscriptions = $this->db->fetchAll(
            "SELECT s.*, p.name as plan_name 
             FROM subscriptions s
             JOIN plans p ON s.plan_id = p.id
             WHERE s.user_id = :user_id
             ORDER BY s.created_at DESC",
            ['user_id' => $id]
        );
        
        // Buscar chaves de API
        $apiKeys = $this->db->fetchAll(
            "SELECT * FROM api_keys WHERE user_id = :user_id ORDER BY created_at DESC",
            ['user_id' => $id]
        );
        
        // Estatísticas de uso
        $usageStats = $this->db->fetch(
            "SELECT
                COUNT(*) as total_requests,
                COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_requests,
                COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as month_requests
             FROM api_usage
             WHERE user_id = :user_id",
            ['user_id' => $id]
        );

        // Buscar planos ativos para o modal de alteração
        $plans = $this->db->fetchAll("SELECT * FROM plans WHERE status = 'active' ORDER BY price ASC");

        $this->view('admin.client-detail', [
            'client' => $client,
            'subscriptions' => $subscriptions,
            'apiKeys' => $apiKeys,
            'usageStats' => $usageStats,
            'plans' => $plans
        ]);
    }
    
    public function updateClient($id) {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return redirect(url("/admin/clients/{$id}"));
        }
        
        $client = $this->db->fetch("SELECT * FROM users WHERE id = :id AND role = 'client'", ['id' => $id]);
        
        if (!$client) {
            $this->flash('error', 'Cliente não encontrado');
            return redirect(url('/admin/clients'));
        }
        
        $action = $this->input('action');
        
        switch ($action) {
            case 'update_status':
                $status = $this->input('status');
                if (in_array($status, ['active', 'inactive', 'suspended'])) {
                    // Atualizar status do usuário
                    $this->db->update('users', ['status' => $status], 'id = :id', ['id' => $id]);

                    // Se o cliente foi suspenso, suspender todas as chaves API e assinaturas
                    if ($status === 'suspended') {
                        // Suspender chaves API
                        $this->db->query(
                            "UPDATE api_keys SET status = 'suspended' WHERE user_id = :user_id AND status = 'active'",
                            ['user_id' => $id]
                        );

                        // Suspender assinaturas ativas
                        $this->db->query(
                            "UPDATE subscriptions SET status = 'suspended' WHERE user_id = :user_id AND status = 'active'",
                            ['user_id' => $id]
                        );

                        $this->flash('success', 'Cliente suspenso! Todas as licenças API e assinaturas foram suspensas automaticamente.');
                    }
                    // Se o cliente foi reativado, reativar chaves e assinaturas suspensas
                    elseif ($status === 'active') {
                        // Reativar chaves API suspensas
                        $this->db->query(
                            "UPDATE api_keys SET status = 'active' WHERE user_id = :user_id AND status = 'suspended'",
                            ['user_id' => $id]
                        );

                        // Reativar assinaturas suspensas (apenas se não expiraram)
                        $this->db->query(
                            "UPDATE subscriptions SET status = 'active'
                             WHERE user_id = :user_id AND status = 'suspended'
                             AND (ends_at IS NULL OR ends_at > NOW())",
                            ['user_id' => $id]
                        );

                        $this->flash('success', 'Cliente reativado! Todas as licenças API e assinaturas válidas foram reativadas automaticamente.');
                    } else {
                        $this->flash('success', 'Status do cliente atualizado com sucesso!');
                    }
                } else {
                    $this->flash('error', 'Status inválido');
                }
                break;
                
            case 'suspend_keys':
                $this->db->query(
                    "UPDATE api_keys SET status = 'suspended' WHERE user_id = :user_id AND status = 'active'",
                    ['user_id' => $id]
                );
                $this->flash('success', 'Todas as chaves do cliente foram suspensas!');
                break;
                
            case 'activate_keys':
                $this->db->query(
                    "UPDATE api_keys SET status = 'active' WHERE user_id = :user_id AND status = 'suspended'",
                    ['user_id' => $id]
                );
                $this->flash('success', 'Todas as chaves do cliente foram reativadas!');
                break;

            case 'edit_client':
                $validation = $this->validate([
                    'name' => 'required|min:2|max:255',
                    'email' => 'required|email|max:255',
                    'phone' => 'nullable|max:20'
                ]);

                if ($validation !== true) {
                    $this->flash('error', 'Dados inválidos: ' . implode(', ', array_values($validation)));
                    break;
                }

                $name = $this->input('name');
                $email = $this->input('email');
                $phone = $this->input('phone');

                // Verificar se o email já existe para outro usuário
                if ($email !== $client['email']) {
                    $existingUser = $this->db->fetch(
                        "SELECT id FROM users WHERE email = :email AND id != :id",
                        ['email' => $email, 'id' => $id]
                    );

                    if ($existingUser) {
                        $this->flash('error', 'Este email já está sendo usado por outro usuário');
                        break;
                    }
                }

                $this->db->update('users', [
                    'name' => $name,
                    'email' => $email,
                    'phone' => $phone
                ], 'id = :id', ['id' => $id]);

                $this->flash('success', 'Dados do cliente atualizados com sucesso!');
                break;

            case 'change_plan':
                $planId = $this->input('plan_id');

                if (!$planId) {
                    $this->flash('error', 'Selecione um plano válido');
                    break;
                }

                $plan = $this->db->fetch("SELECT * FROM plans WHERE id = :id AND status = 'active'", ['id' => $planId]);
                if (!$plan) {
                    $this->flash('error', 'Plano não encontrado ou inativo');
                    break;
                }

                try {
                    $this->db->beginTransaction();

                    // Desativar assinatura atual
                    $this->db->query(
                        "UPDATE subscriptions SET status = 'cancelled' WHERE user_id = :user_id AND status = 'active'",
                        ['user_id' => $id]
                    );

                    // Criar nova assinatura
                    $subscriptionId = $this->db->insert('subscriptions', [
                        'user_id' => $id,
                        'plan_id' => $planId,
                        'status' => 'active',
                        'starts_at' => date('Y-m-d H:i:s'),
                        'ends_at' => $plan['billing_cycle'] === 'lifetime' ? null : date('Y-m-d H:i:s', strtotime('+1 month')),
                        'next_billing_date' => $plan['billing_cycle'] === 'lifetime' ? null : date('Y-m-d H:i:s', strtotime('+1 month')),
                        'payment_status' => 'paid'
                    ]);

                    $this->db->commit();
                    $this->flash('success', 'Plano do cliente alterado com sucesso!');

                } catch (Exception $e) {
                    $this->db->rollback();
                    $this->flash('error', 'Erro ao alterar plano: ' . $e->getMessage());
                }
                break;

            case 'remove_key':
                $keyId = $this->input('key_id');

                if (!$keyId) {
                    $this->flash('error', 'ID da chave não fornecido');
                    break;
                }

                // Verificar se a chave pertence ao cliente
                $key = $this->db->fetch(
                    "SELECT * FROM api_keys WHERE id = :key_id AND user_id = :user_id",
                    ['key_id' => $keyId, 'user_id' => $id]
                );

                if (!$key) {
                    $this->flash('error', 'Chave não encontrada');
                    break;
                }

                $this->db->delete('api_keys', 'id = :id', ['id' => $keyId]);
                $this->flash('success', 'Chave removida com sucesso!');
                break;

            case 'generate_key':
                $keyName = $this->input('key_name', 'Nova Chave');

                // Buscar assinatura ativa do cliente
                $subscription = $this->db->fetch(
                    "SELECT * FROM subscriptions WHERE user_id = :user_id AND status = 'active' ORDER BY created_at DESC LIMIT 1",
                    ['user_id' => $id]
                );

                if (!$subscription) {
                    $this->flash('error', 'Cliente não possui assinatura ativa');
                    break;
                }

                try {
                    $apiKey = Auth::generateApiKey($id, $subscription['id'], $keyName);
                    $this->flash('success', 'Nova chave gerada com sucesso!');
                } catch (Exception $e) {
                    $this->flash('error', 'Erro ao gerar chave: ' . $e->getMessage());
                }
                break;

            default:
                $this->flash('error', 'Ação inválida');
        }
        
        return redirect(url("/admin/clients/{$id}"));
    }
    
    public function settings() {
        // Buscar todas as configurações
        $settings = $this->db->fetchAll("SELECT * FROM settings ORDER BY key_name");
        
        // Organizar por categoria
        $settingsGrouped = [];
        foreach ($settings as $setting) {
            $category = explode('_', $setting['key_name'])[0];
            $settingsGrouped[$category][] = $setting;
        }
        
        $this->view('admin.settings', [
            'settingsGrouped' => $settingsGrouped
        ]);
    }
    
    public function updateSettings() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return redirect(url('/admin/settings'));
        }
        
        $settings = $this->input('settings', []);
        
        foreach ($settings as $key => $value) {
            $this->db->update('settings', ['value' => $value], 'key_name = :key', ['key' => $key]);
        }
        
        $this->flash('success', 'Configurações atualizadas com sucesso!');
        return redirect(url('/admin/settings'));
    }
    
    public function account() {
        $user = Auth::user();

        $this->view('admin.account', [
            'user' => $user
        ]);
    }

    public function apiDocs() {
        // Verificar se a tabela api_usage existe
        $tableExists = false;
        try {
            $this->db->query("DESCRIBE api_usage");
            $tableExists = true;
        } catch (Exception $e) {
            // Tabela não existe
        }

        $apiStats = null;
        $popularEndpoints = [];

        if ($tableExists) {
            try {
                // Buscar estatísticas da API
                $apiStats = $this->db->fetch("
                    SELECT
                        COUNT(*) as total_requests,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(CASE WHEN status_code >= 200 AND status_code < 300 THEN 1 END) as successful_requests,
                        COUNT(CASE WHEN status_code >= 400 THEN 1 END) as failed_requests
                    FROM api_usage
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                ");

                // Endpoints mais utilizados
                $popularEndpoints = $this->db->fetchAll("
                    SELECT
                        endpoint,
                        COUNT(*) as requests
                    FROM api_usage
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    GROUP BY endpoint
                    ORDER BY requests DESC
                    LIMIT 10
                ");
            } catch (Exception $e) {
                // Erro ao buscar dados, usar valores padrão
                $apiStats = null;
                $popularEndpoints = [];
            }
        }

        // Valores padrão se não houver dados
        if (!$apiStats) {
            $apiStats = [
                'total_requests' => 0,
                'unique_users' => 0,
                'successful_requests' => 0,
                'failed_requests' => 0
            ];
        }

        $this->view('admin.api-docs', [
            'apiStats' => $apiStats,
            'popularEndpoints' => $popularEndpoints
        ]);
    }

    public function updateAccount() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return redirect(url('/admin/account'));
        }

        // Validar dados
        $validation = $this->validate([
            'name' => 'required|min:2|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|max:20'
        ]);

        if ($validation !== true) {
            return $this->withErrors($validation)->withInput()->redirect(url('/admin/account'));
        }

        $user = Auth::user();
        $name = $this->input('name');
        $email = $this->input('email');
        $currentPassword = $this->input('current_password');
        $newPassword = $this->input('new_password');
        $confirmPassword = $this->input('confirm_password');

        // Verificar se o email já existe para outro usuário
        if ($email !== $user['email']) {
            $existingUser = $this->db->fetch(
                "SELECT id FROM users WHERE email = :email AND id != :id",
                ['email' => $email, 'id' => $user['id']]
            );

            if ($existingUser) {
                $this->flash('error', 'Este email já está sendo usado por outro usuário');
                return $this->withInput()->redirect(url('/admin/account'));
            }
        }

        $updateData = [
            'name' => $name,
            'email' => $email,
            'phone' => $this->input('phone')
        ];

        // Se foi fornecida uma nova senha
        if (!empty($newPassword)) {
            // Validar senha atual
            if (empty($currentPassword) || !password_verify($currentPassword, $user['password'])) {
                $this->flash('error', 'Senha atual incorreta');
                return $this->withInput()->redirect(url('/admin/account'));
            }

            // Validar nova senha (mais rigorosa para admin)
            if (strlen($newPassword) < 8) {
                $this->flash('error', 'A nova senha deve ter pelo menos 8 caracteres');
                return $this->withInput()->redirect(url('/admin/account'));
            }

            if ($newPassword !== $confirmPassword) {
                $this->flash('error', 'A confirmação da senha não confere');
                return $this->withInput()->redirect(url('/admin/account'));
            }

            $updateData['password'] = password_hash($newPassword, PASSWORD_ARGON2ID);
        }

        // Atualizar dados
        if ($this->db->update('users', $updateData, 'id = :id', ['id' => $user['id']])) {
            $this->flash('success', 'Dados atualizados com sucesso!');
        } else {
            $this->flash('error', 'Erro ao atualizar dados');
        }

        redirect(url('/admin/account'));
    }

    public function createClient() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            return $this->json(['success' => false, 'message' => 'Token de segurança inválido'], 400);
        }

        // Validar dados
        $validation = $this->validate([
            'name' => 'required|min:2|max:255',
            'email' => 'required|email|max:255',
            'password' => 'required|min:6'
        ]);

        if ($validation !== true) {
            return $this->json(['success' => false, 'message' => 'Dados inválidos', 'errors' => $validation], 422);
        }

        $name = $this->input('name');
        $email = $this->input('email');
        $password = $this->input('password');
        $planId = $this->input('plan_id');

        // Verificar se o email já existe
        $existingUser = $this->db->fetch("SELECT id FROM users WHERE email = :email", ['email' => $email]);
        if ($existingUser) {
            return $this->json(['success' => false, 'message' => 'Este email já está sendo usado'], 400);
        }

        try {
            $this->db->beginTransaction();

            // Criar usuário
            $userId = $this->db->insert('users', [
                'name' => $name,
                'email' => $email,
                'password' => password_hash($password, PASSWORD_ARGON2ID),
                'role' => 'client',
                'status' => 'active',
                'email_verified_at' => date('Y-m-d H:i:s')
            ]);

            if (!$userId) {
                throw new Exception('Erro ao criar usuário');
            }

            $userId = $this->db->lastInsertId();

            // Se foi selecionado um plano, criar assinatura
            if ($planId) {
                $plan = $this->db->fetch("SELECT * FROM plans WHERE id = :id AND status = 'active'", ['id' => $planId]);
                if ($plan) {
                    $subscriptionId = $this->db->insert('subscriptions', [
                        'user_id' => $userId,
                        'plan_id' => $planId,
                        'status' => 'active',
                        'starts_at' => date('Y-m-d H:i:s'),
                        'ends_at' => $plan['billing_cycle'] === 'lifetime' ? null : date('Y-m-d H:i:s', strtotime('+1 month')),
                        'next_billing_date' => $plan['billing_cycle'] === 'lifetime' ? null : date('Y-m-d H:i:s', strtotime('+1 month')),
                        'payment_status' => 'paid'
                    ]);

                    if ($subscriptionId) {
                        $subscriptionId = $this->db->lastInsertId();

                        // Gerar chave de API automaticamente
                        Auth::generateApiKey($userId, $subscriptionId, 'Chave Principal');
                    }
                }
            }

            $this->db->commit();

            return $this->json([
                'success' => true,
                'message' => 'Cliente criado com sucesso!',
                'client_id' => $userId
            ]);

        } catch (Exception $e) {
            $this->db->rollback();
            return $this->json(['success' => false, 'message' => 'Erro ao criar cliente: ' . $e->getMessage()], 500);
        }
    }

    public function plans() {
        $plans = $this->db->fetchAll("SELECT * FROM plans ORDER BY price ASC");

        $this->view('admin.plans', [
            'plans' => $plans
        ]);
    }

    public function createPlan() {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return redirect(url('/admin/plans'));
        }

        // Validar dados
        $validation = $this->validate([
            'name' => 'required|min:2|max:255',
            'description' => 'required|min:10',
            'price' => 'required',
            'billing_cycle' => 'required'
        ]);

        if ($validation !== true) {
            return $this->withErrors($validation)->withInput()->redirect(url('/admin/plans'));
        }

        $features = $this->input('features', []);
        $featuresJson = json_encode(array_filter($features));

        $planData = [
            'name' => $this->input('name'),
            'description' => $this->input('description'),
            'price' => (float) $this->input('price'),
            'billing_cycle' => $this->input('billing_cycle'),
            'features' => $featuresJson,
            'limits_per_month' => $this->input('limits_per_month') ?: null,
            'max_sites' => $this->input('max_sites') ?: null,
            'status' => 'active'
        ];

        if ($this->db->insert('plans', $planData)) {
            $this->flash('success', 'Plano criado com sucesso!');
        } else {
            $this->flash('error', 'Erro ao criar plano');
        }

        redirect(url('/admin/plans'));
    }

    public function updatePlan($id) {
        // Verificar CSRF token
        if (!$this->verifyCsrfToken($this->input('_token'))) {
            $this->flash('error', 'Token de segurança inválido');
            return redirect(url('/admin/plans'));
        }

        $plan = $this->db->fetch("SELECT * FROM plans WHERE id = :id", ['id' => $id]);
        if (!$plan) {
            $this->flash('error', 'Plano não encontrado');
            return redirect(url('/admin/plans'));
        }

        $action = $this->input('action');

        if ($action === 'toggle_status') {
            $newStatus = $plan['status'] === 'active' ? 'inactive' : 'active';
            $this->db->update('plans', ['status' => $newStatus], 'id = :id', ['id' => $id]);
            $this->flash('success', 'Status do plano atualizado!');
        } elseif ($action === 'update') {
            $features = $this->input('features', []);
            $featuresJson = json_encode(array_filter($features));

            $planData = [
                'name' => $this->input('name'),
                'description' => $this->input('description'),
                'price' => (float) $this->input('price'),
                'billing_cycle' => $this->input('billing_cycle'),
                'features' => $featuresJson,
                'limits_per_month' => $this->input('limits_per_month') ?: null,
                'max_sites' => $this->input('max_sites') ?: null
            ];

            if ($this->db->update('plans', $planData, 'id = :id', ['id' => $id])) {
                $this->flash('success', 'Plano atualizado com sucesso!');
            } else {
                $this->flash('error', 'Erro ao atualizar plano');
            }
        }

        redirect(url('/admin/plans'));
    }
}
