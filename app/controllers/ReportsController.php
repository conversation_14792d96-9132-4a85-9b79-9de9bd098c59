<?php
/**
 * Controller de Relatórios
 * Sistema de Dashboard E1Copy AI
 */

class ReportsController extends Controller {
    
    public function postsClients() {
        // Buscar posts pendentes de todos os sites usando a nova API
        $pendingPostsData = $this->getPendingPostsFromApi();

        // Buscar estatísticas de posts por cliente/site
        $search = $this->input('search', '');
        $status = $this->input('status', '');
        $orderBy = $this->input('order_by', 'last_activity');
        $orderDir = $this->input('order_dir', 'desc');

        // Validar ordenação
        $allowedOrderBy = ['site_name', 'total_posts', 'pending_posts', 'last_activity', 'user_name'];
        $allowedOrderDir = ['asc', 'desc'];

        if (!in_array($orderBy, $allowedOrderBy)) {
            $orderBy = 'last_activity';
        }

        if (!in_array($orderDir, $allowedOrderDir)) {
            $orderDir = 'desc';
        }

        try {
            // Query simplificada focando apenas nos sites
            $baseQuery = "
                SELECT
                    u.id as user_id,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    cs.id as site_id,
                    cs.site_name,
                    cs.site_url,
                    cs.status as connection_status,
                    cs.last_connection as last_check_at,
                    cs.created_at as site_created_at,
                    cs.updated_at as site_updated_at,

                    -- Inicializar contadores (serão atualizados pela API)
                    0 as total_posts,
                    0 as pending_posts,
                    0 as completed_posts,
                    0 as failed_posts,
                    0 as published_posts,
                    0 as queue_items,
                    0 as queue_pending,

                    -- Última atividade baseada no site
                    COALESCE(cs.last_connection, cs.updated_at, cs.created_at) as last_activity,

                    -- Status inicial
                    '' as last_post_status

                FROM users u
                INNER JOIN client_sites cs ON u.id = cs.user_id
                WHERE u.role = 'client'
                AND cs.status = 'connected'
            ";
        } catch (Exception $e) {
            // Se der erro, usar versão simplificada
            return $this->postsClientsSimple();
        }
        
        $conditions = [];
        $params = [];
        
        // Filtro de busca
        if ($search) {
            $conditions[] = "(u.name LIKE :search OR u.email LIKE :search OR cs.site_name LIKE :search OR cs.site_url LIKE :search)";
            $params['search'] = "%{$search}%";
        }
        
        // Filtro de status
        if ($status) {
            switch ($status) {
                case 'active':
                    $conditions[] = "u.status = 'active' AND cs.status = 'connected'";
                    break;
                case 'inactive':
                    $conditions[] = "u.status != 'active' OR cs.status != 'connected'";
                    break;
                case 'has_errors':
                    $conditions[] = "EXISTS (SELECT 1 FROM generated_posts gp3 WHERE gp3.user_id = u.id AND gp3.generation_status = 'failed')";
                    break;
                case 'has_pending':
                    $conditions[] = "EXISTS (SELECT 1 FROM content_queue cq2 WHERE cq2.user_id = u.id AND cq2.status = 'pending')";
                    break;
            }
        }
        
        // Adicionar condições à query
        if (!empty($conditions)) {
            $baseQuery .= " AND " . implode(" AND ", $conditions);
        }
        
        // Ordenação (sem GROUP BY pois não há agregação)
        switch ($orderBy) {
            case 'site_name':
                $baseQuery .= " ORDER BY cs.site_name {$orderDir}";
                break;
            case 'total_posts':
                $baseQuery .= " ORDER BY cs.site_name {$orderDir}"; // Será reordenado após API
                break;
            case 'pending_posts':
                $baseQuery .= " ORDER BY cs.site_name {$orderDir}"; // Será reordenado após API
                break;
            case 'user_name':
                $baseQuery .= " ORDER BY u.name {$orderDir}";
                break;
            case 'last_activity':
            default:
                $baseQuery .= " ORDER BY last_activity {$orderDir}";
                break;
        }
        
        $sites = $this->db->fetchAll($baseQuery, $params);
        
        // Processar dados para exibição
        foreach ($sites as &$site) {
            // Processar status do último post
            if ($site['last_post_status']) {
                $statusParts = explode('|', $site['last_post_status']);
                $site['last_generation_status'] = $statusParts[0] ?? '';
                $site['last_publish_status'] = $statusParts[1] ?? '';
                $site['last_error_message'] = $statusParts[2] ?? '';
            } else {
                $site['last_generation_status'] = '';
                $site['last_publish_status'] = '';
                $site['last_error_message'] = '';
            }
            
            // Determinar status geral
            $site['overall_status'] = $this->determineOverallStatus($site);
        }
        
        // Estatísticas gerais baseadas nos dados da API
        $generalStats = [
            'total_clients' => count(array_unique(array_column($sites, 'user_id'))),
            'total_sites' => count($sites),
            'total_posts' => $pendingPostsData['summary']['total_posts'] ?? 0,
            'total_pending' => $pendingPostsData['summary']['total_posts'] ?? 0,
            'total_failed' => 0, // Será calculado após combinar dados
            'total_queue_items' => 0 // Será calculado após combinar dados
        ];
        
        // Combinar dados dos sites com posts pendentes
        $sites = $this->combineSitesWithPendingPosts($sites, $pendingPostsData);

        // Reordenar sites após combinar dados da API
        $sites = $this->reorderSitesAfterApiData($sites, $orderBy, $orderDir);

        $this->view('admin.reports.posts-clients', [
            'sites' => $sites,
            'generalStats' => $generalStats,
            'pendingPostsData' => $pendingPostsData,
            'search' => $search,
            'status' => $status,
            'orderBy' => $orderBy,
            'orderDir' => $orderDir,
            'aiSystemInstalled' => true
        ]);
    }

    /**
     * Busca posts pendentes de todos os sites diretamente
     */
    private function getPendingPostsFromApi() {
        try {
            // Buscar todos os sites conectados com suas chaves de API
            $sites = $this->db->fetchAll("
                SELECT cs.*, u.name as user_name, u.email, ak.api_key
                FROM client_sites cs
                LEFT JOIN users u ON cs.user_id = u.id
                LEFT JOIN api_keys ak ON cs.api_key_id = ak.id
                WHERE cs.status = 'connected'
                AND u.status = 'active'
                AND ak.status = 'active'
                ORDER BY cs.site_name
            ");

            $allPosts = [];
            $siteStats = [];

            foreach ($sites as $site) {
                $siteData = [
                    'site_id' => $site['id'],
                    'site_name' => $site['site_name'],
                    'site_url' => $site['site_url'],
                    'user_name' => $site['user_name'],
                    'user_email' => $site['email'],
                    'posts' => [],
                    'total_posts' => 0,
                    'status' => 'error',
                    'error_message' => null
                ];

                try {
                    // Tentar múltiplos endpoints
                    $endpoints = [
                        '/wp-json/e1copy-ai/v1/products?processed=0',
                        '/wp-json/e1copy-ai/v1/posts?processed=0'
                    ];

                    $posts = [];
                    $lastError = null;
                    $successfulEndpoint = null;

                    foreach ($endpoints as $endpoint) {
                        $apiUrl = rtrim($site['site_url'], '/') . $endpoint;

                        $response = $this->makeHttpRequest($apiUrl, [
                            'X-E1Copy-API-Key: ' . $site['api_key']
                        ]);

                        if ($response['success']) {
                            $endpointPosts = [];

                            // Tentar diferentes estruturas de resposta
                            if (isset($response['data']['posts'])) {
                                // Estrutura: { "data": { "posts": [...] } }
                                $endpointPosts = $response['data']['posts'];
                            } elseif (isset($response['data']['data']['posts'])) {
                                // Estrutura: { "data": { "data": { "posts": [...] } } }
                                $endpointPosts = $response['data']['data']['posts'];
                            } elseif (isset($response['data']['data']) && is_array($response['data']['data'])) {
                                // Estrutura: { "data": { "data": [...] } }
                                $endpointPosts = $response['data']['data'];
                            } elseif (isset($response['data']) && is_array($response['data'])) {
                                // Estrutura: { "data": [...] } (array direto)
                                $endpointPosts = $response['data'];
                            } elseif (is_array($response['data'])) {
                                // Estrutura: [...] (array direto na raiz)
                                $endpointPosts = $response['data'];
                            }

                            // Garantir que $endpointPosts é um array
                            if (!is_array($endpointPosts)) {
                                $endpointPosts = [];
                            }

                            // Se encontrou posts, usar este endpoint
                            if (count($endpointPosts) > 0) {
                                $posts = $endpointPosts;
                                $successfulEndpoint = $endpoint;
                                break;
                            } elseif (empty($posts)) {
                                // Se ainda não tem posts, manter este resultado (mesmo que vazio)
                                $posts = $endpointPosts;
                                $successfulEndpoint = $endpoint;
                            }
                        } else {
                            $lastError = $response['error'] ?? 'Erro desconhecido ao acessar API do site';
                        }
                    }

                    if ($successfulEndpoint) {
                        // Adicionar informações do site a cada post
                        foreach ($posts as &$post) {
                            $post['site_info'] = [
                                'site_id' => $site['id'],
                                'site_name' => $site['site_name'],
                                'site_url' => $site['site_url'],
                                'user_name' => $site['user_name'],
                                'user_email' => $site['email']
                            ];
                        }

                        $siteData['posts'] = $posts;
                        $siteData['total_posts'] = count($posts);
                        $siteData['status'] = 'success';
                        $siteData['endpoint_used'] = $successfulEndpoint;

                        // Adicionar posts ao array geral
                        $allPosts = array_merge($allPosts, $posts);
                    } else {
                        $siteData['error_message'] = $lastError ?? 'Nenhum endpoint respondeu com sucesso';
                    }
                } catch (Exception $e) {
                    $siteData['error_message'] = 'Erro de conexão: ' . $e->getMessage();
                }

                $siteStats[] = $siteData;
            }

            // Organizar posts por site/cliente - INCLUIR TODOS OS SITES
            $postsBySite = [];
            $postsByClient = [];

            foreach ($siteStats as $siteData) {


                // SEMPRE incluir o site, mesmo sem posts
                $postsBySite[] = [
                    'site_info' => [
                        'site_id' => $siteData['site_id'],
                        'site_name' => $siteData['site_name'],
                        'site_url' => $siteData['site_url'],
                        'user_name' => $siteData['user_name'],
                        'user_email' => $siteData['user_email']
                    ],
                    'posts' => $siteData['posts'],
                    'total_posts' => $siteData['total_posts'],
                    'status' => $siteData['status']
                ];

                // Posts por cliente (agrupados por email) - SEMPRE incluir
                $clientKey = $siteData['user_email'];
                if (!isset($postsByClient[$clientKey])) {
                    $postsByClient[$clientKey] = [
                        'client_info' => [
                            'user_name' => $siteData['user_name'],
                            'user_email' => $siteData['user_email']
                        ],
                        'sites' => [],
                        'total_posts' => 0,
                        'posts' => []
                    ];
                }

                $postsByClient[$clientKey]['sites'][] = [
                    'site_id' => $siteData['site_id'],
                    'site_name' => $siteData['site_name'],
                    'site_url' => $siteData['site_url'],
                    'posts_count' => $siteData['total_posts'],
                    'status' => $siteData['status']
                ];

                $postsByClient[$clientKey]['total_posts'] += $siteData['total_posts'];
                if ($siteData['total_posts'] > 0) {
                    $postsByClient[$clientKey]['posts'] = array_merge(
                        $postsByClient[$clientKey]['posts'],
                        $siteData['posts']
                    );
                }
            }

            // Calcular estatísticas corretas
            $sitesWithPosts = array_filter($siteStats, function($s) { return $s['total_posts'] > 0; });
            $sitesConnected = array_filter($siteStats, function($s) { return $s['status'] === 'success'; });



            return [
                'summary' => [
                    'total_sites' => count($sites),
                    'total_posts' => count($allPosts),
                    'active_sites' => count($sitesConnected), // Sites que responderam com sucesso
                    'sites_with_posts' => count($sitesWithPosts), // Sites que têm posts
                    'active_clients' => count($postsByClient)
                ],
                'posts_by_site' => $postsBySite,
                'posts_by_client' => array_values($postsByClient),
                'all_posts' => $allPosts, // Manter compatibilidade
                'sites_stats' => $siteStats, // Dados detalhados de cada site
                'timestamp' => date('Y-m-d H:i:s')
            ];

        } catch (Exception $e) {
            error_log('Exceção ao buscar posts pendentes: ' . $e->getMessage());
            return $this->getEmptyPendingPostsData();
        }
    }

    /**
     * Faz requisição HTTP para APIs externas
     */
    private function makeHttpRequest($url, $headers = [], $timeout = 30) {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_USERAGENT => 'E1Copy-Dashboard/1.0'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            return [
                'success' => false,
                'error' => 'Erro cURL: ' . $error,
                'http_code' => 0
            ];
        }

        $data = json_decode($response, true);

        return [
            'success' => $httpCode >= 200 && $httpCode < 300,
            'data' => $data,
            'http_code' => $httpCode,
            'error' => $httpCode >= 400 ? 'HTTP Error ' . $httpCode : null
        ];
    }

    /**
     * Retorna estrutura vazia para posts pendentes
     */
    private function getEmptyPendingPostsData() {
        return [
            'total_sites' => 0,
            'total_posts' => 0,
            'posts' => [],
            'sites_stats' => [],
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Combina dados dos sites com informações de posts pendentes
     */
    private function combineSitesWithPendingPosts($sites, $pendingPostsData) {
        // Criar mapa de estatísticas por site usando URL como chave principal
        $siteStatsMap = [];
        $siteStatsMapById = [];

        foreach ($pendingPostsData['sites_stats'] ?? [] as $siteStats) {
            // Mapear por ID do site
            if (!empty($siteStats['site_id'])) {
                $siteStatsMapById[$siteStats['site_id']] = $siteStats;
            }

            // Mapear por URL do site (mais confiável)
            if (!empty($siteStats['site_url'])) {
                $cleanUrl = rtrim($siteStats['site_url'], '/');
                $siteStatsMap[$cleanUrl] = $siteStats;
            }
        }

        // Atualizar dados dos sites
        foreach ($sites as &$site) {
            $siteId = $site['site_id'];
            $siteUrl = rtrim($site['site_url'] ?? '', '/');

            $stats = null;

            // Tentar encontrar por URL primeiro (mais confiável)
            if (!empty($siteUrl) && isset($siteStatsMap[$siteUrl])) {
                $stats = $siteStatsMap[$siteUrl];
            }
            // Fallback para ID do site
            elseif (!empty($siteId) && isset($siteStatsMapById[$siteId])) {
                $stats = $siteStatsMapById[$siteId];
            }

            if ($stats) {
                // Atualizar contadores de posts pendentes
                $site['pending_posts'] = $stats['total_posts'];
                $site['api_status'] = $stats['status'];
                $site['api_error'] = $stats['error_message'];

                // Atualizar status geral baseado na API
                if ($stats['status'] === 'success') {
                    $site['overall_status'] = $stats['total_posts'] > 0 ? 'warning' : 'success';
                } else {
                    $site['overall_status'] = 'error';
                }


            } else {
                // Site não encontrado na API
                $site['pending_posts'] = 0;
                $site['api_status'] = 'not_found';
                $site['api_error'] = 'Site não encontrado na sincronização ou sem posts pendentes';
                $site['overall_status'] = 'info';


            }
        }

        return $sites;
    }

    /**
     * Reordena sites após combinar com dados da API
     */
    private function reorderSitesAfterApiData($sites, $orderBy, $orderDir) {
        usort($sites, function($a, $b) use ($orderBy, $orderDir) {
            $result = 0;

            switch ($orderBy) {
                case 'site_name':
                    $result = strcasecmp($a['site_name'], $b['site_name']);
                    break;
                case 'total_posts':
                    $result = ($a['total_posts'] ?? 0) <=> ($b['total_posts'] ?? 0);
                    break;
                case 'pending_posts':
                    $aPending = ($a['pending_posts'] ?? 0) + ($a['queue_pending'] ?? 0);
                    $bPending = ($b['pending_posts'] ?? 0) + ($b['queue_pending'] ?? 0);
                    $result = $aPending <=> $bPending;
                    break;
                case 'user_name':
                    $result = strcasecmp($a['user_name'], $b['user_name']);
                    break;
                case 'last_activity':
                default:
                    $aTime = strtotime($a['last_activity'] ?? '1970-01-01');
                    $bTime = strtotime($b['last_activity'] ?? '1970-01-01');
                    $result = $aTime <=> $bTime;
                    break;
            }

            return $orderDir === 'desc' ? -$result : $result;
        });

        return $sites;
    }

    private function determineOverallStatus($site) {
        // Se tem erros recentes
        if ($site['failed_posts'] > 0 || !empty($site['last_error_message'])) {
            return 'error';
        }
        
        // Se tem itens pendentes
        if ($site['pending_posts'] > 0 || $site['queue_pending'] > 0) {
            return 'pending';
        }
        
        // Se o site não está conectado
        if ($site['connection_status'] !== 'connected') {
            return 'warning';
        }
        
        // Se tem posts publicados
        if ($site['published_posts'] > 0) {
            return 'success';
        }
        
        // Status neutro
        return 'info';
    }
    


    public function syncAllSites() {
        try {
            $syncService = new SiteSyncService();
            $results = $syncService->syncAllSites();

            $successCount = count(array_filter($results, function($r) { return $r['success']; }));
            $totalCount = count($results);

            $this->json([
                'success' => true,
                'message' => "Sincronização concluída: {$successCount}/{$totalCount} sites sincronizados",
                'data' => $results
            ]);
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro na sincronização: ' . $e->getMessage()
            ]);
        }
    }

    public function syncSite($id) {
        try {
            $syncService = new SiteSyncService();
            $result = $syncService->syncSiteById($id);

            $this->json($result);
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro na sincronização: ' . $e->getMessage()
            ]);
        }
    }

    public function testPostsApi() {
        try {
            // Buscar posts pendentes usando o método interno
            $pendingPostsData = $this->getPendingPostsFromApi();

            $this->json([
                'success' => true,
                'message' => 'Teste da API de posts concluído',
                'data' => $pendingPostsData,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro no teste da API: ' . $e->getMessage(),
                'error' => $e->getTraceAsString()
            ]);
        }
    }



    private function checkAiTablesExist() {
        try {
            $this->db->fetch("SELECT 1 FROM generated_posts LIMIT 1");
            $this->db->fetch("SELECT 1 FROM content_queue LIMIT 1");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    private function postsClientsSimple() {
        // Versão simplificada sem tabelas de IA
        $search = $this->input('search', '');
        $orderBy = $this->input('order_by', 'user_name');
        $orderDir = $this->input('order_dir', 'asc');

        $conditions = [];
        $params = [];

        // Filtro de busca
        if ($search) {
            $conditions[] = "(u.name LIKE :search OR u.email LIKE :search OR cs.site_name LIKE :search OR cs.site_url LIKE :search)";
            $params['search'] = "%{$search}%";
        }

        $whereClause = !empty($conditions) ? "AND " . implode(" AND ", $conditions) : "";

        // Query simplificada
        $sites = $this->db->fetchAll("
            SELECT
                u.id as user_id,
                u.name as user_name,
                u.email as user_email,
                u.status as user_status,
                cs.id as site_id,
                cs.site_name,
                cs.site_url,
                cs.status as connection_status,
                cs.last_connection as last_check_at,
                0 as total_posts,
                0 as pending_posts,
                0 as completed_posts,
                0 as failed_posts,
                0 as published_posts,
                0 as queue_items,
                0 as queue_pending,
                cs.last_connection as last_activity,
                '' as last_post_status
            FROM users u
            LEFT JOIN client_sites cs ON u.id = cs.user_id
            WHERE u.role = 'client' {$whereClause}
            ORDER BY u.name {$orderDir}
        ", $params);

        // Processar dados
        foreach ($sites as &$site) {
            $site['last_generation_status'] = '';
            $site['last_publish_status'] = '';
            $site['last_error_message'] = '';
            $site['overall_status'] = $site['connection_status'] === 'connected' ? 'info' : 'warning';
        }

        // Estatísticas gerais simplificadas
        $generalStats = $this->db->fetch("
            SELECT
                COUNT(DISTINCT u.id) as total_clients,
                COUNT(DISTINCT cs.id) as total_sites,
                0 as total_posts,
                0 as total_pending,
                0 as total_failed,
                0 as total_queue_items
            FROM users u
            LEFT JOIN client_sites cs ON u.id = cs.user_id
            WHERE u.role = 'client'
        ");

        $this->view('admin.reports.posts-clients', [
            'sites' => $sites,
            'generalStats' => $generalStats,
            'search' => $search,
            'status' => '',
            'orderBy' => $orderBy,
            'orderDir' => $orderDir,
            'aiSystemInstalled' => false
        ]);
    }
}
