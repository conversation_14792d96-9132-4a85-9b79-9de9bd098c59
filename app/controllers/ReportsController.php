<?php
/**
 * Controller de Relatórios
 * Sistema de Dashboard E1Copy AI
 */

class ReportsController extends Controller {
    
    public function postsClients() {
        // Verificar se as tabelas de IA existem
        $tablesExist = $this->checkAiTablesExist();

        if (!$tablesExist) {
            // Usar query simplificada sem tabelas de IA
            return $this->postsClientsSimple();
        }

        // Buscar estatísticas de posts por cliente/site
        $search = $this->input('search', '');
        $status = $this->input('status', '');
        $orderBy = $this->input('order_by', 'last_activity');
        $orderDir = $this->input('order_dir', 'desc');

        // Validar ordenação
        $allowedOrderBy = ['site_name', 'total_posts', 'pending_posts', 'last_activity', 'user_name'];
        $allowedOrderDir = ['asc', 'desc'];

        if (!in_array($orderBy, $allowedOrderBy)) {
            $orderBy = 'last_activity';
        }

        if (!in_array($orderDir, $allowedOrderDir)) {
            $orderDir = 'desc';
        }

        try {
            // Construir query base
            $baseQuery = "
                SELECT
                    u.id as user_id,
                    u.name as user_name,
                    u.email as user_email,
                    u.status as user_status,
                    cs.id as site_id,
                    cs.site_name,
                    cs.site_url,
                    cs.status as connection_status,
                    cs.last_connection as last_check_at,

                    -- Estatísticas de posts
                    COUNT(gp.id) as total_posts,
                    COUNT(CASE WHEN gp.generation_status = 'pending' THEN 1 END) as pending_posts,
                    COUNT(CASE WHEN gp.generation_status = 'completed' THEN 1 END) as completed_posts,
                    COUNT(CASE WHEN gp.generation_status = 'failed' THEN 1 END) as failed_posts,
                    COUNT(CASE WHEN gp.publish_status = 'published' THEN 1 END) as published_posts,

                    -- Estatísticas da fila
                    COUNT(cq.id) as queue_items,
                    COUNT(CASE WHEN cq.status = 'pending' THEN 1 END) as queue_pending,

                    -- Última atividade
                    MAX(GREATEST(
                        COALESCE(gp.updated_at, '1970-01-01'),
                        COALESCE(cq.updated_at, '1970-01-01'),
                        COALESCE(cs.last_connection, '1970-01-01')
                    )) as last_activity,

                    -- Status do último post
                    (SELECT CONCAT(gp2.generation_status, '|', gp2.publish_status, '|', gp2.error_message)
                     FROM generated_posts gp2
                     WHERE gp2.user_id = u.id AND (gp2.site_id = cs.id OR gp2.site_url = cs.site_url)
                     ORDER BY gp2.updated_at DESC
                     LIMIT 1) as last_post_status

                FROM users u
                LEFT JOIN client_sites cs ON u.id = cs.user_id
                LEFT JOIN generated_posts gp ON u.id = gp.user_id AND (gp.site_id = cs.id OR gp.site_url = cs.site_url)
                LEFT JOIN content_queue cq ON u.id = cq.user_id AND (cq.site_id = cs.id OR cq.site_url = cs.site_url)
                WHERE u.role = 'client'
            ";
        } catch (Exception $e) {
            // Se der erro, usar versão simplificada
            return $this->postsClientsSimple();
        }
        
        $conditions = [];
        $params = [];
        
        // Filtro de busca
        if ($search) {
            $conditions[] = "(u.name LIKE :search OR u.email LIKE :search OR cs.site_name LIKE :search OR cs.site_url LIKE :search)";
            $params['search'] = "%{$search}%";
        }
        
        // Filtro de status
        if ($status) {
            switch ($status) {
                case 'active':
                    $conditions[] = "u.status = 'active' AND cs.status = 'connected'";
                    break;
                case 'inactive':
                    $conditions[] = "u.status != 'active' OR cs.status != 'connected'";
                    break;
                case 'has_errors':
                    $conditions[] = "EXISTS (SELECT 1 FROM generated_posts gp3 WHERE gp3.user_id = u.id AND gp3.generation_status = 'failed')";
                    break;
                case 'has_pending':
                    $conditions[] = "EXISTS (SELECT 1 FROM content_queue cq2 WHERE cq2.user_id = u.id AND cq2.status = 'pending')";
                    break;
            }
        }
        
        // Adicionar condições à query
        if (!empty($conditions)) {
            $baseQuery .= " AND " . implode(" AND ", $conditions);
        }
        
        // Agrupar e ordenar
        $baseQuery .= " GROUP BY u.id, cs.id";
        
        // Ordenação
        switch ($orderBy) {
            case 'site_name':
                $baseQuery .= " ORDER BY cs.site_name {$orderDir}";
                break;
            case 'total_posts':
                $baseQuery .= " ORDER BY total_posts {$orderDir}";
                break;
            case 'pending_posts':
                $baseQuery .= " ORDER BY (pending_posts + queue_pending) {$orderDir}";
                break;
            case 'user_name':
                $baseQuery .= " ORDER BY u.name {$orderDir}";
                break;
            case 'last_activity':
            default:
                $baseQuery .= " ORDER BY last_activity {$orderDir}";
                break;
        }
        
        $sites = $this->db->fetchAll($baseQuery, $params);
        
        // Processar dados para exibição
        foreach ($sites as &$site) {
            // Processar status do último post
            if ($site['last_post_status']) {
                $statusParts = explode('|', $site['last_post_status']);
                $site['last_generation_status'] = $statusParts[0] ?? '';
                $site['last_publish_status'] = $statusParts[1] ?? '';
                $site['last_error_message'] = $statusParts[2] ?? '';
            } else {
                $site['last_generation_status'] = '';
                $site['last_publish_status'] = '';
                $site['last_error_message'] = '';
            }
            
            // Determinar status geral
            $site['overall_status'] = $this->determineOverallStatus($site);
        }
        
        // Estatísticas gerais
        $generalStats = $this->db->fetch("
            SELECT 
                COUNT(DISTINCT u.id) as total_clients,
                COUNT(DISTINCT cs.id) as total_sites,
                COUNT(gp.id) as total_posts,
                COUNT(CASE WHEN gp.generation_status = 'pending' THEN 1 END) as total_pending,
                COUNT(CASE WHEN gp.generation_status = 'failed' THEN 1 END) as total_failed,
                COUNT(cq.id) as total_queue_items
            FROM users u
            LEFT JOIN client_sites cs ON u.id = cs.user_id
            LEFT JOIN generated_posts gp ON u.id = gp.user_id
            LEFT JOIN content_queue cq ON u.id = cq.user_id
            WHERE u.role = 'client'
        ");
        
        $this->view('admin.reports.posts-clients', [
            'sites' => $sites,
            'generalStats' => $generalStats,
            'search' => $search,
            'status' => $status,
            'orderBy' => $orderBy,
            'orderDir' => $orderDir
        ]);
    }
    
    private function determineOverallStatus($site) {
        // Se tem erros recentes
        if ($site['failed_posts'] > 0 || !empty($site['last_error_message'])) {
            return 'error';
        }
        
        // Se tem itens pendentes
        if ($site['pending_posts'] > 0 || $site['queue_pending'] > 0) {
            return 'pending';
        }
        
        // Se o site não está conectado
        if ($site['connection_status'] !== 'connected') {
            return 'warning';
        }
        
        // Se tem posts publicados
        if ($site['published_posts'] > 0) {
            return 'success';
        }
        
        // Status neutro
        return 'info';
    }
    
    public function aiUsage() {
        // Relatório de uso da IA
        $this->view('admin.reports.ai-usage', [
            'title' => 'Relatório de Uso da IA'
        ]);
    }

    public function syncAllSites() {
        try {
            $syncService = new SiteSyncService();
            $results = $syncService->syncAllSites();

            $successCount = count(array_filter($results, function($r) { return $r['success']; }));
            $totalCount = count($results);

            $this->json([
                'success' => true,
                'message' => "Sincronização concluída: {$successCount}/{$totalCount} sites sincronizados",
                'data' => $results
            ]);
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro na sincronização: ' . $e->getMessage()
            ]);
        }
    }

    public function syncSite($id) {
        try {
            $syncService = new SiteSyncService();
            $result = $syncService->syncSiteById($id);

            $this->json($result);
        } catch (Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Erro na sincronização: ' . $e->getMessage()
            ]);
        }
    }

    private function checkAiTablesExist() {
        try {
            $this->db->fetch("SELECT 1 FROM generated_posts LIMIT 1");
            $this->db->fetch("SELECT 1 FROM content_queue LIMIT 1");
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    private function postsClientsSimple() {
        // Versão simplificada sem tabelas de IA
        $search = $this->input('search', '');
        $orderBy = $this->input('order_by', 'user_name');
        $orderDir = $this->input('order_dir', 'asc');

        $conditions = [];
        $params = [];

        // Filtro de busca
        if ($search) {
            $conditions[] = "(u.name LIKE :search OR u.email LIKE :search OR cs.site_name LIKE :search OR cs.site_url LIKE :search)";
            $params['search'] = "%{$search}%";
        }

        $whereClause = !empty($conditions) ? "AND " . implode(" AND ", $conditions) : "";

        // Query simplificada
        $sites = $this->db->fetchAll("
            SELECT
                u.id as user_id,
                u.name as user_name,
                u.email as user_email,
                u.status as user_status,
                cs.id as site_id,
                cs.site_name,
                cs.site_url,
                cs.status as connection_status,
                cs.last_connection as last_check_at,
                0 as total_posts,
                0 as pending_posts,
                0 as completed_posts,
                0 as failed_posts,
                0 as published_posts,
                0 as queue_items,
                0 as queue_pending,
                cs.last_connection as last_activity,
                '' as last_post_status
            FROM users u
            LEFT JOIN client_sites cs ON u.id = cs.user_id
            WHERE u.role = 'client' {$whereClause}
            ORDER BY u.name {$orderDir}
        ", $params);

        // Processar dados
        foreach ($sites as &$site) {
            $site['last_generation_status'] = '';
            $site['last_publish_status'] = '';
            $site['last_error_message'] = '';
            $site['overall_status'] = $site['connection_status'] === 'connected' ? 'info' : 'warning';
        }

        // Estatísticas gerais simplificadas
        $generalStats = $this->db->fetch("
            SELECT
                COUNT(DISTINCT u.id) as total_clients,
                COUNT(DISTINCT cs.id) as total_sites,
                0 as total_posts,
                0 as total_pending,
                0 as total_failed,
                0 as total_queue_items
            FROM users u
            LEFT JOIN client_sites cs ON u.id = cs.user_id
            WHERE u.role = 'client'
        ");

        $this->view('admin.reports.posts-clients', [
            'sites' => $sites,
            'generalStats' => $generalStats,
            'search' => $search,
            'status' => '',
            'orderBy' => $orderBy,
            'orderDir' => $orderDir,
            'aiSystemInstalled' => false
        ]);
    }
}
