<?php
$title = 'Dashboard - E1Copy AI';
$pageTitle = 'Dashboard do Cliente';
ob_start();
?>

<div class="row">
    <!-- Estatísticas -->
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= $stats['total_requests'] ?? 0 ?></h4>
                        <p class="card-text">Total de Requisições</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= $stats['today_requests'] ?? 0 ?></h4>
                        <p class="card-text">Hoje</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= $stats['month_requests'] ?? 0 ?></h4>
                        <p class="card-text">Este Mês</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= count($sites) ?></h4>
                        <p class="card-text">Sites Cadastrados</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-globe fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Minhas Licenças -->
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-certificate me-2"></i>
                    Minhas Licenças
                </h5>
                <a href="<?= url('/client/sites') ?>" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-cog me-1"></i>
                    Gerenciar Sites
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($sites)): ?>
                    <div class="row">
                        <?php foreach ($sites as $site): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100 border-<?= $site['status'] === 'connected' ? 'success' : ($site['status'] === 'pending' ? 'warning' : 'danger') ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0"><?= e($site['site_name']) ?></h6>
                                            <span class="badge bg-<?= $site['status'] === 'connected' ? 'success' : ($site['status'] === 'pending' ? 'warning' : 'danger') ?> fs-6">
                                                <?php
                                                switch($site['status']) {
                                                    case 'connected': echo 'Conectado'; break;
                                                    case 'pending': echo 'Pendente'; break;
                                                    case 'disconnected': echo 'Desconectado'; break;
                                                    case 'suspended': echo 'Suspenso'; break;
                                                    default: echo ucfirst($site['status']);
                                                }
                                                ?>
                                            </span>
                                        </div>

                                        <p class="card-text small text-muted mb-2">
                                            <i class="fas fa-link me-1"></i>
                                            <?= e(parse_url($site['site_url'], PHP_URL_HOST)) ?>
                                        </p>

                                        <?php if ($site['api_key_name']): ?>
                                            <p class="card-text small mb-2">
                                                <i class="fas fa-key me-1"></i>
                                                <strong>Licença:</strong> <?= e($site['api_key_name']) ?>
                                                <span class="badge bg-<?= $site['api_key_status'] === 'active' ? 'success' : 'danger' ?> ms-1">
                                                    <?= ucfirst($site['api_key_status']) ?>
                                                </span>
                                            </p>
                                        <?php endif; ?>

                                        <?php if ($site['plugin_version']): ?>
                                            <p class="card-text small text-muted mb-2">
                                                <i class="fas fa-plug me-1"></i>
                                                Plugin v<?= e($site['plugin_version']) ?>
                                            </p>
                                        <?php endif; ?>

                                        <?php if ($site['last_connection']): ?>
                                            <p class="card-text small text-muted mb-0">
                                                <i class="fas fa-clock me-1"></i>
                                                Última conexão: <?= date('d/m/Y H:i', strtotime($site['last_connection'])) ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="card-footer">
                                        <div class="btn-group w-100" role="group">
                                            <button type="button" class="btn btn-outline-success btn-sm"
                                                    onclick="showSiteApiKey(<?= $site['id'] ?>)" title="Ver Licença">
                                                <i class="fas fa-key"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                    onclick="checkSiteConnection(<?= $site['id'] ?>)" title="Verificar Conexão">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <a href="<?= e($site['site_url']) ?>" target="_blank"
                                               class="btn btn-outline-info btn-sm" title="Visitar Site">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                        <h5>Nenhum site cadastrado</h5>
                        <p class="text-muted">Adicione seu primeiro site para começar a usar o E1Copy AI.</p>
                        <a href="<?= url('/client/sites') ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Adicionar Primeiro Site
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informações da Assinatura -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    Minha Assinatura
                </h5>
            </div>
            <div class="card-body">
                <?php if ($subscription): ?>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Plano:</strong><br>
                            <span class="badge bg-primary fs-6"><?= e($subscription['plan_name']) ?></span>
                        </div>
                        <div class="col-sm-6">
                            <strong>Status:</strong><br>
                            <span class="badge bg-<?= $subscription['status'] === 'active' ? 'success' : 'danger' ?> fs-6">
                                <?= ucfirst($subscription['status']) ?>
                            </span>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Limite Mensal:</strong><br>
                            <?= $subscription['limits_per_month'] ? number_format($subscription['limits_per_month']) . ' requisições' : 'Ilimitado' ?>
                        </div>
                        <div class="col-sm-6">
                            <strong>Sites Permitidos:</strong><br>
                            <?= $subscription['max_sites'] ?? 'Ilimitado' ?>
                        </div>
                    </div>
                    
                    <?php if ($subscription['ends_at']): ?>
                        <hr>
                        <div class="row">
                            <div class="col-12">
                                <strong>Próxima Cobrança:</strong><br>
                                <?= date('d/m/Y', strtotime($subscription['next_billing_date'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>Nenhuma assinatura ativa</h5>
                        <p class="text-muted">Entre em contato com o suporte para ativar sua assinatura.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

</div>



<!-- Modal para exibir chave API -->
<div class="modal fade" id="siteApiKeyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Licença API do Site</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Importante:</strong> Mantenha sua licença API segura e não a compartilhe.
                </div>

                <div class="mb-3">
                    <label class="form-label">Site:</label>
                    <div class="form-control-plaintext" id="siteApiKeySiteName"></div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Sua Licença API:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="siteApiKeyValue" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copySiteApiKey()">
                            <i class="fas fa-copy"></i> Copiar
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="<?= url('/client/sites') ?>" class="btn btn-primary">
                    <i class="fas fa-cog me-1"></i>
                    Gerenciar Sites
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
// Mostrar chave API do site
function showSiteApiKey(siteId) {
    const siteData = <?= json_encode($sites) ?>.find(s => s.id == siteId);

    if (siteData) {
        // Buscar chave API do site
        fetch(`<?= url('/client/sites/api-key/') ?>${siteId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('siteApiKeySiteName').textContent = siteData.site_name;
                document.getElementById('siteApiKeyValue').value = data.api_key;

                new bootstrap.Modal(document.getElementById('siteApiKeyModal')).show();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao buscar chave API');
        });
    }
}

// Copiar chave para clipboard
function copySiteApiKey() {
    const input = document.getElementById('siteApiKeyValue');
    input.select();
    document.execCommand('copy');

    // Feedback visual
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i> Copiado!';
    setTimeout(() => {
        button.innerHTML = originalHTML;
    }, 2000);
}

// Verificar conexão do site
function checkSiteConnection(siteId) {
    const formData = new FormData();
    formData.append('_token', '<?= $csrf_token ?>');

    fetch(`<?= url('/client/sites/check/') ?>${siteId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Erro: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Erro:', error);
        alert('Erro ao verificar conexão');
    });
}
</script>


