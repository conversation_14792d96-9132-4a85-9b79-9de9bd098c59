<?php include __DIR__ . '/../../layouts/client-header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <?php if ($postType === 'article'): ?>
                        <i class="fas fa-file-alt text-primary"></i> Criar Artigo Simples
                    <?php else: ?>
                        <i class="fas fa-star text-warning"></i> Criar Produto Review
                    <?php endif; ?>
                </h1>
                <a href="/client/posts/create" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_GET['error']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST" action="/client/posts" id="postForm">
                <input type="hidden" name="post_type" value="<?= htmlspecialchars($postType) ?>">
                <input type="hidden" name="site_id" value="<?= htmlspecialchars($site['id']) ?>">
                
                <div class="row">
                    <div class="col-md-8">
                        <!-- Informações do Site -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-globe"></i> Site de Destino
                                </h6>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <strong><?= htmlspecialchars($site['site_name']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= htmlspecialchars($site['site_url']) ?></small>
                                    </div>
                                    <div class="ms-auto">
                                        <span class="badge bg-success">Conectado</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Conteúdo Principal -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">Conteúdo Principal</h6>
                                
                                <!-- Título -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">Título *</label>
                                    <input type="text" class="form-control" id="title" name="title" required
                                           placeholder="Digite o título do post...">
                                </div>

                                <!-- Resumo/Excerpt -->
                                <div class="mb-3">
                                    <label for="excerpt" class="form-label">Resumo/Excerpt</label>
                                    <textarea class="form-control" id="excerpt" name="excerpt" rows="3"
                                              placeholder="Breve descrição do post (opcional)..."></textarea>
                                    <div class="form-text">Usado para SEO e listagens de posts.</div>
                                </div>

                                <!-- Conteúdo -->
                                <div class="mb-3">
                                    <label for="content" class="form-label">Conteúdo *</label>
                                    <textarea class="form-control" id="content" name="content" rows="15" required
                                              placeholder="Digite o conteúdo do post..."></textarea>
                                    <div class="form-text">Você pode usar HTML básico para formatação.</div>
                                </div>
                            </div>
                        </div>

                        <?php if ($postType === 'product_review'): ?>
                        <!-- Conteúdo Principal -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-edit"></i> Conteúdo Principal
                                </h6>

                                <div class="mb-3">
                                    <label for="product_name" class="form-label">Nome do Produto *</label>
                                    <input type="text" class="form-control" id="product_name" name="product_name" required
                                           placeholder="Digite o nome do produto...">
                                </div>

                                <div class="mb-3">
                                    <label for="product_description" class="form-label">Descrição do Produto *</label>
                                    <textarea class="form-control" id="product_description" name="product_description" rows="6" required
                                              placeholder="Descreva detalhadamente o produto, suas características, benefícios e funcionalidades..."></textarea>
                                    <div class="form-text">Esta será a descrição principal do produto no review.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="post_cover" class="form-label">Capa do Post</label>
                                    <input type="url" class="form-control" id="post_cover" name="post_cover"
                                           placeholder="https://exemplo.com/imagem-capa.jpg">
                                    <div class="form-text">URL da imagem que será usada como capa do post.</div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="keyword" class="form-label">Palavra-Chave Principal *</label>
                                            <input type="text" class="form-control" id="keyword" name="keyword" required
                                                   placeholder="palavra-chave principal">
                                            <div class="form-text">Palavra-chave principal para SEO.</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="category" class="form-label">Categoria *</label>
                                            <input type="text" class="form-control" id="category" name="category" required
                                                   placeholder="Nome da categoria">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="tags" class="form-label">Tags</label>
                                            <input type="text" class="form-control" id="tags" name="tags"
                                                   placeholder="tag1, tag2, tag3">
                                            <div class="form-text">Separe as tags por vírgula.</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="existing_tags" class="form-label">Tags Existentes</label>
                                            <input type="text" class="form-control" id="existing_tags" name="existing_tags"
                                                   placeholder="tag-existente1, tag-existente2">
                                            <div class="form-text">Tags que já existem no site (para reutilização).</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="youtube_video" class="form-label">Vídeo YouTube</label>
                                    <input type="url" class="form-control" id="youtube_video" name="youtube_video"
                                           placeholder="https://www.youtube.com/watch?v=...">
                                    <div class="form-text">URL do vídeo do YouTube relacionado ao produto.</div>
                                </div>
                            </div>
                        </div>

                        <!-- Imagens do Produto -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-images"></i> Imagens do Produto
                                    <span class="badge bg-info">Mínimo 5 imagens</span>
                                </h6>

                                <div id="product-images-container">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="product_image_1" class="form-label">Imagem 1 *</label>
                                                <input type="url" class="form-control product-image" id="product_image_1" name="product_images[]" required
                                                       placeholder="https://exemplo.com/imagem1.jpg">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="product_image_2" class="form-label">Imagem 2 *</label>
                                                <input type="url" class="form-control product-image" id="product_image_2" name="product_images[]" required
                                                       placeholder="https://exemplo.com/imagem2.jpg">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="product_image_3" class="form-label">Imagem 3 *</label>
                                                <input type="url" class="form-control product-image" id="product_image_3" name="product_images[]" required
                                                       placeholder="https://exemplo.com/imagem3.jpg">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="product_image_4" class="form-label">Imagem 4 *</label>
                                                <input type="url" class="form-control product-image" id="product_image_4" name="product_images[]" required
                                                       placeholder="https://exemplo.com/imagem4.jpg">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="product_image_5" class="form-label">Imagem 5 *</label>
                                                <input type="url" class="form-control product-image" id="product_image_5" name="product_images[]" required
                                                       placeholder="https://exemplo.com/imagem5.jpg">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="product_image_6" class="form-label">Imagem 6</label>
                                                <input type="url" class="form-control product-image" id="product_image_6" name="product_images[]"
                                                       placeholder="https://exemplo.com/imagem6.jpg">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <button type="button" class="btn btn-outline-primary btn-sm" id="add-image-btn">
                                    <i class="fas fa-plus"></i> Adicionar Mais Imagens
                                </button>

                                <div class="form-text mt-2">
                                    <strong>Importante:</strong> São necessárias pelo menos 5 imagens do produto.
                                    Use URLs diretas para as imagens (terminadas em .jpg, .png, .webp, etc.).
                                </div>
                            </div>
                        </div>

                        <!-- Informações Adicionais do Produto -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle"></i> Informações Adicionais
                                </h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_url" class="form-label">URL do Produto</label>
                                            <input type="url" class="form-control" id="product_url" name="product_url"
                                                   placeholder="https://loja.com/produto">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_price" class="form-label">Preço (R$)</label>
                                            <input type="number" class="form-control" id="product_price" name="product_price"
                                                   step="0.01" min="0" placeholder="0.00">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_rating" class="form-label">Avaliação (1-5 estrelas)</label>
                                            <select class="form-select" id="product_rating" name="product_rating">
                                                <option value="">Selecione...</option>
                                                <option value="1">⭐ 1 estrela</option>
                                                <option value="1.5">⭐ 1.5 estrelas</option>
                                                <option value="2">⭐⭐ 2 estrelas</option>
                                                <option value="2.5">⭐⭐ 2.5 estrelas</option>
                                                <option value="3">⭐⭐⭐ 3 estrelas</option>
                                                <option value="3.5">⭐⭐⭐ 3.5 estrelas</option>
                                                <option value="4">⭐⭐⭐⭐ 4 estrelas</option>
                                                <option value="4.5">⭐⭐⭐⭐ 4.5 estrelas</option>
                                                <option value="5">⭐⭐⭐⭐⭐ 5 estrelas</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="affiliate_link" class="form-label">Link de Afiliado</label>
                                            <input type="url" class="form-control" id="affiliate_link" name="affiliate_link"
                                                   placeholder="https://afiliado.com/produto">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_pros" class="form-label">Pontos Positivos</label>
                                            <textarea class="form-control" id="product_pros" name="product_pros" rows="4"
                                                      placeholder="Liste os pontos positivos do produto..."></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_cons" class="form-label">Pontos Negativos</label>
                                            <textarea class="form-control" id="product_cons" name="product_cons" rows="4"
                                                      placeholder="Liste os pontos negativos do produto..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-4">
                        <!-- SEO e Categorização -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-search"></i> SEO e Categorização
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="keywords" class="form-label">Palavras-chave</label>
                                    <input type="text" class="form-control" id="keywords" name="keywords"
                                           placeholder="palavra1, palavra2, palavra3...">
                                    <div class="form-text">Separe as palavras-chave por vírgula.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="category" class="form-label">Categoria</label>
                                    <input type="text" class="form-control" id="category" name="category"
                                           placeholder="Nome da categoria...">
                                </div>

                                <div class="mb-3">
                                    <label for="tags" class="form-label">Tags</label>
                                    <input type="text" class="form-control" id="tags" name="tags"
                                           placeholder="tag1, tag2, tag3...">
                                    <div class="form-text">Separe as tags por vírgula.</div>
                                </div>
                            </div>
                        </div>

                        <!-- Ações -->
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-cog"></i> Ações
                                </h6>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" name="action" value="save_draft" class="btn btn-outline-secondary">
                                        <i class="fas fa-save"></i> Salvar Rascunho
                                    </button>
                                    <button type="submit" name="action" value="publish" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Publicar Agora
                                    </button>
                                </div>
                                
                                <hr>
                                
                                <div class="text-muted small">
                                    <p><strong>Rascunho:</strong> Salva o post sem publicar. Você pode editá-lo depois.</p>
                                    <p><strong>Publicar:</strong> Envia o post para a fila de publicação. Será processado automaticamente.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Auto-resize textarea
document.getElementById('content').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});

// Validação do formulário
document.getElementById('postForm').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const content = document.getElementById('content').value.trim();
    
    if (!title) {
        e.preventDefault();
        alert('Por favor, digite um título para o post.');
        document.getElementById('title').focus();
        return;
    }
    
    if (!content) {
        e.preventDefault();
        alert('Por favor, digite o conteúdo do post.');
        document.getElementById('content').focus();
        return;
    }
    
    <?php if ($postType === 'product_review'): ?>
    const productName = document.getElementById('product_name').value.trim();
    const productDescription = document.getElementById('product_description').value.trim();
    const keyword = document.getElementById('keyword').value.trim();
    const category = document.getElementById('category').value.trim();

    if (!productName) {
        e.preventDefault();
        alert('Por favor, digite o nome do produto.');
        document.getElementById('product_name').focus();
        return;
    }

    if (!productDescription) {
        e.preventDefault();
        alert('Por favor, digite a descrição do produto.');
        document.getElementById('product_description').focus();
        return;
    }

    if (!keyword) {
        e.preventDefault();
        alert('Por favor, digite a palavra-chave principal.');
        document.getElementById('keyword').focus();
        return;
    }

    if (!category) {
        e.preventDefault();
        alert('Por favor, digite a categoria.');
        document.getElementById('category').focus();
        return;
    }

    // Validar imagens obrigatórias
    const requiredImages = document.querySelectorAll('input[name="product_images[]"][required]');
    let missingImages = 0;

    requiredImages.forEach(function(input) {
        if (!input.value.trim()) {
            missingImages++;
        }
    });

    if (missingImages > 0) {
        e.preventDefault();
        alert(`Por favor, adicione todas as 5 imagens obrigatórias do produto. Faltam ${missingImages} imagem(ns).`);
        return;
    }
    <?php endif; ?>
    
    // Confirmar publicação
    if (e.submitter && e.submitter.value === 'publish') {
        if (!confirm('Tem certeza que deseja publicar este post agora? Ele será enviado para processamento.')) {
            e.preventDefault();
            return;
        }
    }
});

// Contador de imagens
let imageCount = 6;

// Adicionar mais campos de imagem
document.getElementById('add-image-btn')?.addEventListener('click', function() {
    imageCount++;
    const container = document.getElementById('product-images-container');

    const newRow = document.createElement('div');
    newRow.className = 'row';
    newRow.innerHTML = `
        <div class="col-md-6">
            <div class="mb-3">
                <label for="product_image_${imageCount}" class="form-label">Imagem ${imageCount}</label>
                <div class="input-group">
                    <input type="url" class="form-control product-image" id="product_image_${imageCount}" name="product_images[]"
                           placeholder="https://exemplo.com/imagem${imageCount}.jpg">
                    <button type="button" class="btn btn-outline-danger remove-image-btn">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;

    container.appendChild(newRow);

    // Adicionar evento de remoção
    newRow.querySelector('.remove-image-btn').addEventListener('click', function() {
        newRow.remove();
    });
});

// Auto-resize textarea da descrição do produto
document.getElementById('product_description')?.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});

// Contador de caracteres para o título
document.getElementById('title').addEventListener('input', function() {
    const maxLength = 500;
    const currentLength = this.value.length;
    
    // Criar ou atualizar contador
    let counter = document.getElementById('title-counter');
    if (!counter) {
        counter = document.createElement('div');
        counter.id = 'title-counter';
        counter.className = 'form-text';
        this.parentNode.appendChild(counter);
    }
    
    counter.textContent = `${currentLength}/${maxLength} caracteres`;
    counter.className = currentLength > maxLength ? 'form-text text-danger' : 'form-text text-muted';
});
</script>

<?php include __DIR__ . '/../../layouts/client-footer.php'; ?>
