<?php include __DIR__ . '/../../layouts/client-header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Meus Posts</h1>
                <a href="/client/posts/create" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Novo Post
                </a>
            </div>

            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_GET['success']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_GET['error']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Estatísticas -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $stats['total'] ?></h4>
                                    <p class="mb-0">Total de Posts</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $stats['drafts'] ?></h4>
                                    <p class="mb-0">Rascunhos</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-edit fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $stats['pending'] ?></h4>
                                    <p class="mb-0">Pendentes</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $stats['published'] ?></h4>
                                    <p class="mb-0">Publicados</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filtros -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Buscar</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?= htmlspecialchars($search) ?>" 
                                   placeholder="Título, conteúdo ou palavras-chave...">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Todos os status</option>
                                <option value="draft" <?= $status === 'draft' ? 'selected' : '' ?>>Rascunho</option>
                                <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>Pendente</option>
                                <option value="processing" <?= $status === 'processing' ? 'selected' : '' ?>>Processando</option>
                                <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>Concluído</option>
                                <option value="published" <?= $status === 'published' ? 'selected' : '' ?>>Publicado</option>
                                <option value="failed" <?= $status === 'failed' ? 'selected' : '' ?>>Falhou</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="post_type" class="form-label">Tipo</label>
                            <select class="form-select" id="post_type" name="post_type">
                                <option value="">Todos os tipos</option>
                                <option value="article" <?= $postType === 'article' ? 'selected' : '' ?>>Artigo Simples</option>
                                <option value="product_review" <?= $postType === 'product_review' ? 'selected' : '' ?>>Produto Review</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i> Filtrar
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Lista de Posts -->
            <div class="card">
                <div class="card-body">
                    <?php if (empty($posts)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Nenhum post encontrado</h5>
                            <p class="text-muted">Comece criando seu primeiro post!</p>
                            <a href="/client/posts/create" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Criar Primeiro Post
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Título</th>
                                        <th>Tipo</th>
                                        <th>Site</th>
                                        <th>Status</th>
                                        <th>Criado em</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($posts as $post): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($post['title']) ?></strong>
                                                    <?php if ($post['excerpt']): ?>
                                                        <br><small class="text-muted"><?= htmlspecialchars(substr($post['excerpt'], 0, 100)) ?>...</small>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($post['post_type'] === 'article'): ?>
                                                    <span class="badge bg-primary">Artigo Simples</span>
                                                <?php else: ?>
                                                    <span class="badge bg-info">Produto Review</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong><?= htmlspecialchars($post['site_name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($post['site_url']) ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClasses = [
                                                    'draft' => 'bg-secondary',
                                                    'pending' => 'bg-warning',
                                                    'processing' => 'bg-info',
                                                    'completed' => 'bg-success',
                                                    'published' => 'bg-success',
                                                    'failed' => 'bg-danger'
                                                ];
                                                $statusLabels = [
                                                    'draft' => 'Rascunho',
                                                    'pending' => 'Pendente',
                                                    'processing' => 'Processando',
                                                    'completed' => 'Concluído',
                                                    'published' => 'Publicado',
                                                    'failed' => 'Falhou'
                                                ];
                                                ?>
                                                <span class="badge <?= $statusClasses[$post['status']] ?>">
                                                    <?= $statusLabels[$post['status']] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?= date('d/m/Y H:i', strtotime($post['created_at'])) ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="/client/posts/<?= $post['id'] ?>" class="btn btn-outline-primary" title="Ver">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if (in_array($post['status'], ['draft', 'pending', 'failed'])): ?>
                                                        <a href="/client/posts/<?= $post['id'] ?>/edit" class="btn btn-outline-warning" title="Editar">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (in_array($post['status'], ['draft', 'failed'])): ?>
                                                        <button type="button" class="btn btn-outline-danger" title="Excluir"
                                                                onclick="confirmDelete(<?= $post['id'] ?>, '<?= htmlspecialchars($post['title']) ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Paginação -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="Navegação de páginas">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&post_type=<?= urlencode($postType) ?>">Anterior</a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&post_type=<?= urlencode($postType) ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&post_type=<?= urlencode($postType) ?>">Próxima</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmação de exclusão -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmar Exclusão</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja excluir o post "<span id="postTitle"></span>"?</p>
                <p class="text-muted">Esta ação não pode ser desfeita.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Excluir</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(postId, postTitle) {
    document.getElementById('postTitle').textContent = postTitle;
    document.getElementById('deleteForm').action = '/client/posts/' + postId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?php include __DIR__ . '/../../layouts/client-footer.php'; ?>
