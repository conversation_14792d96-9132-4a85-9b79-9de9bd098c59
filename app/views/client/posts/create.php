<?php include __DIR__ . '/../../layouts/client-header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Criar Novo Post</h1>
                <a href="/client/posts" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Voltar
                </a>
            </div>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_GET['error']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title mb-4">Escolha o tipo de post que deseja criar:</h5>
                            
                            <div class="row">
                                <!-- Artigo Simples -->
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 post-type-card" onclick="selectPostType('article')">
                                        <div class="card-body text-center">
                                            <div class="mb-3">
                                                <i class="fas fa-file-alt fa-4x text-primary"></i>
                                            </div>
                                            <h5 class="card-title">Artigo Simples</h5>
                                            <p class="card-text">
                                                Crie artigos informativos, tutoriais, notícias ou qualquer conteúdo textual tradicional.
                                            </p>
                                            <ul class="list-unstyled text-start">
                                                <li><i class="fas fa-check text-success me-2"></i> Título e conteúdo</li>
                                                <li><i class="fas fa-check text-success me-2"></i> Resumo/excerpt</li>
                                                <li><i class="fas fa-check text-success me-2"></i> Palavras-chave</li>
                                                <li><i class="fas fa-check text-success me-2"></i> Categoria e tags</li>
                                                <li><i class="fas fa-check text-success me-2"></i> SEO otimizado</li>
                                            </ul>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-primary w-100">
                                                <i class="fas fa-plus"></i> Criar Artigo
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Produto Review -->
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100 post-type-card" onclick="selectPostType('product_review')">
                                        <div class="card-body text-center">
                                            <div class="mb-3">
                                                <i class="fas fa-star fa-4x text-warning"></i>
                                            </div>
                                            <h5 class="card-title">Produto Review</h5>
                                            <p class="card-text">
                                                Crie reviews detalhadas de produtos com avaliações, prós e contras, e links de afiliado.
                                            </p>
                                            <ul class="list-unstyled text-start">
                                                <li><i class="fas fa-check text-success me-2"></i> Informações do produto</li>
                                                <li><i class="fas fa-check text-success me-2"></i> Avaliação por estrelas</li>
                                                <li><i class="fas fa-check text-success me-2"></i> Prós e contras</li>
                                                <li><i class="fas fa-check text-success me-2"></i> Link de afiliado</li>
                                                <li><i class="fas fa-check text-success me-2"></i> Preço e onde comprar</li>
                                            </ul>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-warning w-100">
                                                <i class="fas fa-star"></i> Criar Review
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Seleção do Site -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">Selecione o site onde o post será publicado:</h6>
                                            
                                            <?php if (empty($sites)): ?>
                                                <div class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    Você não possui sites conectados. 
                                                    <a href="/client/sites" class="alert-link">Conecte um site primeiro</a>.
                                                </div>
                                            <?php else: ?>
                                                <div class="row">
                                                    <?php foreach ($sites as $site): ?>
                                                        <div class="col-md-6 mb-3">
                                                            <div class="card site-card" onclick="selectSite(<?= $site['id'] ?>)">
                                                                <div class="card-body">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input" type="radio" name="site_id" 
                                                                               id="site_<?= $site['id'] ?>" value="<?= $site['id'] ?>">
                                                                        <label class="form-check-label w-100" for="site_<?= $site['id'] ?>">
                                                                            <strong><?= htmlspecialchars($site['site_name']) ?></strong>
                                                                            <br>
                                                                            <small class="text-muted"><?= htmlspecialchars($site['site_url']) ?></small>
                                                                            <br>
                                                                            <span class="badge bg-success">Conectado</span>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.post-type-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.post-type-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.site-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.site-card:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.site-card.selected {
    border-color: #007bff;
    background-color: #e3f2fd;
}
</style>

<script>
let selectedPostType = null;
let selectedSiteId = null;

function selectPostType(type) {
    selectedPostType = type;
    
    // Remove seleção anterior
    document.querySelectorAll('.post-type-card').forEach(card => {
        card.classList.remove('border-primary');
        card.style.borderColor = 'transparent';
    });
    
    // Adiciona seleção atual
    event.currentTarget.classList.add('border-primary');
    event.currentTarget.style.borderColor = '#007bff';
    
    checkCanProceed();
}

function selectSite(siteId) {
    selectedSiteId = siteId;
    
    // Remove seleção anterior
    document.querySelectorAll('.site-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Adiciona seleção atual
    event.currentTarget.classList.add('selected');
    
    // Marca o radio button
    document.getElementById('site_' + siteId).checked = true;
    
    checkCanProceed();
}

function checkCanProceed() {
    if (selectedPostType && selectedSiteId) {
        // Redirecionar para o formulário específico
        window.location.href = '/client/posts/create/form?type=' + selectedPostType + '&site_id=' + selectedSiteId;
    }
}

// Auto-selecionar se houver apenas um site
document.addEventListener('DOMContentLoaded', function() {
    const siteCards = document.querySelectorAll('.site-card');
    if (siteCards.length === 1) {
        const siteId = siteCards[0].querySelector('input[name="site_id"]').value;
        selectSite(parseInt(siteId));
    }
});
</script>

<?php include __DIR__ . '/../../layouts/client-footer.php'; ?>
