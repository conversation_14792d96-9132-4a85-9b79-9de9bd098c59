<?php include __DIR__ . '/../../layouts/client-header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <?php if ($post['post_type'] === 'article'): ?>
                        <i class="fas fa-file-alt text-primary"></i> Editar Artigo Simples
                    <?php else: ?>
                        <i class="fas fa-star text-warning"></i> Editar Produto Review
                    <?php endif; ?>
                </h1>
                <div>
                    <a href="/client/posts/<?= $post['id'] ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar
                    </a>
                </div>
            </div>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_GET['error']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST" action="/client/posts/<?= $post['id'] ?>" id="postForm">
                <div class="row">
                    <div class="col-md-8">
                        <!-- Informações do Site -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-globe"></i> Site de Destino
                                </h6>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <strong><?= htmlspecialchars($post['site_name']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= htmlspecialchars($post['site_url']) ?></small>
                                    </div>
                                    <div class="ms-auto">
                                        <span class="badge bg-success">Conectado</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Conteúdo Principal -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">Conteúdo Principal</h6>
                                
                                <!-- Título -->
                                <div class="mb-3">
                                    <label for="title" class="form-label">Título *</label>
                                    <input type="text" class="form-control" id="title" name="title" required
                                           value="<?= htmlspecialchars($post['title']) ?>"
                                           placeholder="Digite o título do post...">
                                </div>

                                <!-- Resumo/Excerpt -->
                                <div class="mb-3">
                                    <label for="excerpt" class="form-label">Resumo/Excerpt</label>
                                    <textarea class="form-control" id="excerpt" name="excerpt" rows="3"
                                              placeholder="Breve descrição do post (opcional)..."><?= htmlspecialchars($post['excerpt']) ?></textarea>
                                    <div class="form-text">Usado para SEO e listagens de posts.</div>
                                </div>

                                <!-- Conteúdo -->
                                <div class="mb-3">
                                    <label for="content" class="form-label">Conteúdo *</label>
                                    <textarea class="form-control" id="content" name="content" rows="15" required
                                              placeholder="Digite o conteúdo do post..."><?= htmlspecialchars($post['content']) ?></textarea>
                                    <div class="form-text">Você pode usar HTML básico para formatação.</div>
                                </div>
                            </div>
                        </div>

                        <?php if ($post['post_type'] === 'product_review'): ?>
                        <!-- Informações do Produto -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-box"></i> Informações do Produto
                                </h6>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_name" class="form-label">Nome do Produto *</label>
                                            <input type="text" class="form-control" id="product_name" name="product_name" required
                                                   value="<?= htmlspecialchars($post['product_name']) ?>"
                                                   placeholder="Nome do produto...">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_url" class="form-label">URL do Produto</label>
                                            <input type="url" class="form-control" id="product_url" name="product_url"
                                                   value="<?= htmlspecialchars($post['product_url']) ?>"
                                                   placeholder="https://...">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_price" class="form-label">Preço (R$)</label>
                                            <input type="number" class="form-control" id="product_price" name="product_price" 
                                                   step="0.01" min="0" 
                                                   value="<?= $post['product_price'] ?>"
                                                   placeholder="0.00">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_rating" class="form-label">Avaliação (1-5 estrelas)</label>
                                            <select class="form-select" id="product_rating" name="product_rating">
                                                <option value="">Selecione...</option>
                                                <option value="1" <?= $post['product_rating'] == '1' ? 'selected' : '' ?>>⭐ 1 estrela</option>
                                                <option value="1.5" <?= $post['product_rating'] == '1.5' ? 'selected' : '' ?>>⭐ 1.5 estrelas</option>
                                                <option value="2" <?= $post['product_rating'] == '2' ? 'selected' : '' ?>>⭐⭐ 2 estrelas</option>
                                                <option value="2.5" <?= $post['product_rating'] == '2.5' ? 'selected' : '' ?>>⭐⭐ 2.5 estrelas</option>
                                                <option value="3" <?= $post['product_rating'] == '3' ? 'selected' : '' ?>>⭐⭐⭐ 3 estrelas</option>
                                                <option value="3.5" <?= $post['product_rating'] == '3.5' ? 'selected' : '' ?>>⭐⭐⭐ 3.5 estrelas</option>
                                                <option value="4" <?= $post['product_rating'] == '4' ? 'selected' : '' ?>>⭐⭐⭐⭐ 4 estrelas</option>
                                                <option value="4.5" <?= $post['product_rating'] == '4.5' ? 'selected' : '' ?>>⭐⭐⭐⭐ 4.5 estrelas</option>
                                                <option value="5" <?= $post['product_rating'] == '5' ? 'selected' : '' ?>>⭐⭐⭐⭐⭐ 5 estrelas</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_pros" class="form-label">Pontos Positivos</label>
                                            <textarea class="form-control" id="product_pros" name="product_pros" rows="4"
                                                      placeholder="Liste os pontos positivos do produto..."><?= htmlspecialchars($post['product_pros']) ?></textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="product_cons" class="form-label">Pontos Negativos</label>
                                            <textarea class="form-control" id="product_cons" name="product_cons" rows="4"
                                                      placeholder="Liste os pontos negativos do produto..."><?= htmlspecialchars($post['product_cons']) ?></textarea>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="affiliate_link" class="form-label">Link de Afiliado</label>
                                    <input type="url" class="form-control" id="affiliate_link" name="affiliate_link"
                                           value="<?= htmlspecialchars($post['affiliate_link']) ?>"
                                           placeholder="https://...">
                                    <div class="form-text">Link para onde o usuário será direcionado para comprar o produto.</div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-4">
                        <!-- Status Atual -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-info-circle"></i> Status Atual
                                </h6>
                                
                                <?php
                                $statusClasses = [
                                    'draft' => 'bg-secondary',
                                    'pending' => 'bg-warning',
                                    'processing' => 'bg-info',
                                    'completed' => 'bg-success',
                                    'published' => 'bg-success',
                                    'failed' => 'bg-danger'
                                ];
                                $statusLabels = [
                                    'draft' => 'Rascunho',
                                    'pending' => 'Pendente',
                                    'processing' => 'Processando',
                                    'completed' => 'Concluído',
                                    'published' => 'Publicado',
                                    'failed' => 'Falhou'
                                ];
                                ?>
                                <span class="badge <?= $statusClasses[$post['status']] ?> fs-6">
                                    <?= $statusLabels[$post['status']] ?>
                                </span>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        Criado em: <?= date('d/m/Y H:i', strtotime($post['created_at'])) ?>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- SEO e Categorização -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-search"></i> SEO e Categorização
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="keywords" class="form-label">Palavras-chave</label>
                                    <input type="text" class="form-control" id="keywords" name="keywords"
                                           value="<?= htmlspecialchars($post['keywords']) ?>"
                                           placeholder="palavra1, palavra2, palavra3...">
                                    <div class="form-text">Separe as palavras-chave por vírgula.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="category" class="form-label">Categoria</label>
                                    <input type="text" class="form-control" id="category" name="category"
                                           value="<?= htmlspecialchars($post['category']) ?>"
                                           placeholder="Nome da categoria...">
                                </div>

                                <div class="mb-3">
                                    <label for="tags" class="form-label">Tags</label>
                                    <input type="text" class="form-control" id="tags" name="tags"
                                           value="<?= htmlspecialchars($post['tags']) ?>"
                                           placeholder="tag1, tag2, tag3...">
                                    <div class="form-text">Separe as tags por vírgula.</div>
                                </div>
                            </div>
                        </div>

                        <!-- Ações -->
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-cog"></i> Ações
                                </h6>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" name="action" value="save_draft" class="btn btn-outline-secondary">
                                        <i class="fas fa-save"></i> Salvar Rascunho
                                    </button>
                                    <?php if (in_array($post['status'], ['draft', 'failed'])): ?>
                                        <button type="submit" name="action" value="publish" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i> Publicar Agora
                                        </button>
                                    <?php endif; ?>
                                </div>
                                
                                <hr>
                                
                                <div class="text-muted small">
                                    <p><strong>Rascunho:</strong> Salva as alterações sem publicar.</p>
                                    <?php if (in_array($post['status'], ['draft', 'failed'])): ?>
                                        <p><strong>Publicar:</strong> Envia o post para a fila de publicação.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Auto-resize textarea
document.getElementById('content').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});

// Validação do formulário
document.getElementById('postForm').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const content = document.getElementById('content').value.trim();
    
    if (!title) {
        e.preventDefault();
        alert('Por favor, digite um título para o post.');
        document.getElementById('title').focus();
        return;
    }
    
    if (!content) {
        e.preventDefault();
        alert('Por favor, digite o conteúdo do post.');
        document.getElementById('content').focus();
        return;
    }
    
    <?php if ($post['post_type'] === 'product_review'): ?>
    const productName = document.getElementById('product_name').value.trim();
    if (!productName) {
        e.preventDefault();
        alert('Por favor, digite o nome do produto.');
        document.getElementById('product_name').focus();
        return;
    }
    <?php endif; ?>
    
    // Confirmar publicação
    if (e.submitter && e.submitter.value === 'publish') {
        if (!confirm('Tem certeza que deseja publicar este post agora? Ele será enviado para processamento.')) {
            e.preventDefault();
            return;
        }
    }
});

// Contador de caracteres para o título
document.getElementById('title').addEventListener('input', function() {
    const maxLength = 500;
    const currentLength = this.value.length;
    
    // Criar ou atualizar contador
    let counter = document.getElementById('title-counter');
    if (!counter) {
        counter = document.createElement('div');
        counter.id = 'title-counter';
        counter.className = 'form-text';
        this.parentNode.appendChild(counter);
    }
    
    counter.textContent = `${currentLength}/${maxLength} caracteres`;
    counter.className = currentLength > maxLength ? 'form-text text-danger' : 'form-text text-muted';
});

// Trigger inicial do contador
document.getElementById('title').dispatchEvent(new Event('input'));
</script>

<?php include __DIR__ . '/../../layouts/client-footer.php'; ?>
