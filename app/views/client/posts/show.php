<?php include __DIR__ . '/../../layouts/client-header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <?php if ($post['post_type'] === 'article'): ?>
                        <i class="fas fa-file-alt text-primary"></i> Artigo Simples
                    <?php else: ?>
                        <i class="fas fa-star text-warning"></i> Produto Review
                    <?php endif; ?>
                </h1>
                <div>
                    <a href="/client/posts" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar
                    </a>
                    <?php if (in_array($post['status'], ['draft', 'pending', 'failed'])): ?>
                        <a href="/client/posts/<?= $post['id'] ?>/edit" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Editar
                        </a>
                    <?php endif; ?>
                    <?php if ($post['status'] === 'draft'): ?>
                        <form method="POST" action="/client/posts/<?= $post['id'] ?>/publish" style="display: inline;">
                            <button type="submit" class="btn btn-primary" 
                                    onclick="return confirm('Tem certeza que deseja publicar este post?')">
                                <i class="fas fa-paper-plane"></i> Publicar
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>

            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_GET['success']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($_GET['error']) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <!-- Informações Principais -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h2 class="card-title"><?= htmlspecialchars($post['title']) ?></h2>
                            
                            <?php if ($post['excerpt']): ?>
                                <div class="card bg-info bg-opacity-10 border-info mb-3">
                                    <div class="card-body">
                                        <strong>Resumo:</strong> <?= htmlspecialchars($post['excerpt']) ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="post-content">
                                <?= nl2br(htmlspecialchars($post['content'])) ?>
                            </div>
                        </div>
                    </div>

                    <?php if ($post['post_type'] === 'product_review'): ?>
                    <!-- Informações do Produto -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-box"></i> Informações do Produto
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Nome do Produto:</strong><br>
                                    <?= htmlspecialchars($post['product_name']) ?>
                                </div>
                                <?php if ($post['product_rating']): ?>
                                <div class="col-md-6">
                                    <strong>Avaliação:</strong><br>
                                    <?php
                                    $rating = (float)$post['product_rating'];
                                    $fullStars = floor($rating);
                                    $halfStar = ($rating - $fullStars) >= 0.5;
                                    
                                    for ($i = 1; $i <= 5; $i++) {
                                        if ($i <= $fullStars) {
                                            echo '<i class="fas fa-star text-warning"></i>';
                                        } elseif ($i == $fullStars + 1 && $halfStar) {
                                            echo '<i class="fas fa-star-half-alt text-warning"></i>';
                                        } else {
                                            echo '<i class="far fa-star text-muted"></i>';
                                        }
                                    }
                                    echo ' (' . $rating . '/5)';
                                    ?>
                                </div>
                                <?php endif; ?>
                            </div>

                            <?php if ($post['product_price']): ?>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <strong>Preço:</strong><br>
                                    R$ <?= number_format($post['product_price'], 2, ',', '.') ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['product_url']): ?>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <strong>URL do Produto:</strong><br>
                                    <a href="<?= htmlspecialchars($post['product_url']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-external-link-alt"></i> Ver Produto
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['product_pros'] || $post['product_cons']): ?>
                            <div class="row mt-4">
                                <?php if ($post['product_pros']): ?>
                                <div class="col-md-6">
                                    <h6 class="text-success">
                                        <i class="fas fa-thumbs-up"></i> Pontos Positivos
                                    </h6>
                                    <div class="text-success">
                                        <?= nl2br(htmlspecialchars($post['product_pros'])) ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($post['product_cons']): ?>
                                <div class="col-md-6">
                                    <h6 class="text-danger">
                                        <i class="fas fa-thumbs-down"></i> Pontos Negativos
                                    </h6>
                                    <div class="text-danger">
                                        <?= nl2br(htmlspecialchars($post['product_cons'])) ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['affiliate_link']): ?>
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="card bg-warning bg-opacity-10 border-warning">
                                        <div class="card-body">
                                            <strong>Link de Afiliado:</strong><br>
                                            <a href="<?= htmlspecialchars($post['affiliate_link']) ?>" target="_blank" class="btn btn-warning mt-2">
                                                <i class="fas fa-shopping-cart"></i> Comprar Agora
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-4">
                    <!-- Status e Informações -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-info-circle"></i> Informações
                            </h6>
                            
                            <div class="mb-3">
                                <strong>Status:</strong><br>
                                <?php
                                $statusClasses = [
                                    'draft' => 'bg-secondary',
                                    'pending' => 'bg-warning',
                                    'processing' => 'bg-info',
                                    'completed' => 'bg-success',
                                    'published' => 'bg-success',
                                    'failed' => 'bg-danger'
                                ];
                                $statusLabels = [
                                    'draft' => 'Rascunho',
                                    'pending' => 'Pendente',
                                    'processing' => 'Processando',
                                    'completed' => 'Concluído',
                                    'published' => 'Publicado',
                                    'failed' => 'Falhou'
                                ];
                                ?>
                                <span class="badge <?= $statusClasses[$post['status']] ?>">
                                    <?= $statusLabels[$post['status']] ?>
                                </span>
                            </div>

                            <div class="mb-3">
                                <strong>Site:</strong><br>
                                <?= htmlspecialchars($post['site_name']) ?><br>
                                <small class="text-muted"><?= htmlspecialchars($post['site_url']) ?></small>
                            </div>

                            <div class="mb-3">
                                <strong>Criado em:</strong><br>
                                <?= date('d/m/Y H:i', strtotime($post['created_at'])) ?>
                            </div>

                            <?php if ($post['updated_at'] !== $post['created_at']): ?>
                            <div class="mb-3">
                                <strong>Atualizado em:</strong><br>
                                <?= date('d/m/Y H:i', strtotime($post['updated_at'])) ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['published_at']): ?>
                            <div class="mb-3">
                                <strong>Publicado em:</strong><br>
                                <?= date('d/m/Y H:i', strtotime($post['published_at'])) ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['wordpress_post_id']): ?>
                            <div class="mb-3">
                                <strong>ID no WordPress:</strong><br>
                                #<?= $post['wordpress_post_id'] ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['error_message']): ?>
                            <div class="card bg-danger bg-opacity-10 border-danger">
                                <div class="card-body">
                                    <strong class="text-danger">Erro:</strong><br>
                                    <span class="text-danger"><?= htmlspecialchars($post['error_message']) ?></span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- SEO e Categorização -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-search"></i> SEO e Categorização
                            </h6>
                            
                            <?php if ($post['keywords']): ?>
                            <div class="mb-3">
                                <strong>Palavras-chave:</strong><br>
                                <?php
                                $keywords = explode(',', $post['keywords']);
                                foreach ($keywords as $keyword) {
                                    echo '<span class="badge bg-light text-dark me-1">' . htmlspecialchars(trim($keyword)) . '</span>';
                                }
                                ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['category']): ?>
                            <div class="mb-3">
                                <strong>Categoria:</strong><br>
                                <span class="badge bg-primary"><?= htmlspecialchars($post['category']) ?></span>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['tags']): ?>
                            <div class="mb-3">
                                <strong>Tags:</strong><br>
                                <?php
                                $tags = explode(',', $post['tags']);
                                foreach ($tags as $tag) {
                                    echo '<span class="badge bg-secondary me-1">' . htmlspecialchars(trim($tag)) . '</span>';
                                }
                                ?>
                            </div>
                            <?php endif; ?>

                            <?php if ($post['slug']): ?>
                            <div class="mb-3">
                                <strong>Slug:</strong><br>
                                <code><?= htmlspecialchars($post['slug']) ?></code>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.post-content {
    line-height: 1.6;
    font-size: 1.1rem;
}

.post-content p {
    margin-bottom: 1rem;
}

.badge {
    font-size: 0.8rem;
}
</style>

<?php include __DIR__ . '/../../layouts/client-footer.php'; ?>
