<?php
$title = 'Minha Conta - E1Copy AI';
$pageTitle = 'Minha Conta';
ob_start();
?>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    Informações da Conta
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= url('/client/account') ?>">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Nome Completo</label>
                            <input type="text" 
                                   class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
                                   id="name" 
                                   name="name" 
                                   value="<?= e($old['name'] ?? $user['name']) ?>"
                                   required>
                            <?php if (isset($errors['name'])): ?>
                                <div class="invalid-feedback">
                                    <?= e($errors['name'][0]) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" 
                                   class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                   id="email" 
                                   name="email" 
                                   value="<?= e($old['email'] ?? $user['email']) ?>"
                                   required>
                            <?php if (isset($errors['email'])): ?>
                                <div class="invalid-feedback">
                                    <?= e($errors['email'][0]) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6 class="mb-3">
                        <i class="fas fa-lock me-2"></i>
                        Alterar Senha (opcional)
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="current_password" class="form-label">Senha Atual</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="current_password" 
                                   name="current_password"
                                   placeholder="Digite sua senha atual">
                            <div class="form-text">Necessário apenas se quiser alterar a senha</div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="new_password" class="form-label">Nova Senha</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="new_password" 
                                   name="new_password"
                                   placeholder="Nova senha (mín. 6 caracteres)">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="confirm_password" class="form-label">Confirmar Nova Senha</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="confirm_password" 
                                   name="confirm_password"
                                   placeholder="Confirme a nova senha">
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted small">
                            <i class="fas fa-info-circle me-1"></i>
                            Conta criada em: <?= date('d/m/Y H:i', strtotime($user['created_at'])) ?>
                        </div>
                        
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Salvar Alterações
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Informações Adicionais -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Informações da Conta
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Status da Conta:</strong><br>
                        <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'danger' ?> fs-6">
                            <?= ucfirst($user['status']) ?>
                        </span>
                    </div>
                    
                    <div class="col-md-6">
                        <strong>Tipo de Conta:</strong><br>
                        <span class="badge bg-primary fs-6">
                            <?= ucfirst($user['role']) ?>
                        </span>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <strong>Email Verificado:</strong><br>
                        <?php if ($user['email_verified_at']): ?>
                            <span class="text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Verificado em <?= date('d/m/Y', strtotime($user['email_verified_at'])) ?>
                            </span>
                        <?php else: ?>
                            <span class="text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Não verificado
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-6">
                        <strong>Última Atualização:</strong><br>
                        <?= date('d/m/Y H:i', strtotime($user['updated_at'])) ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Zona de Perigo -->
        <div class="card mt-4 border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Zona de Perigo
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    Se você não está mais satisfeito com nossos serviços, pode solicitar o cancelamento da sua conta.
                    Esta ação é irreversível e todos os seus dados serão removidos permanentemente.
                </p>
                
                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelAccountModal">
                    <i class="fas fa-user-times me-2"></i>
                    Solicitar Cancelamento da Conta
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Cancelamento -->
<div class="modal fade" id="cancelAccountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">Cancelar Conta</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Atenção!</strong> Esta ação não pode ser desfeita.
                </div>
                
                <p>Ao cancelar sua conta:</p>
                <ul>
                    <li>Todas as suas chaves de API serão revogadas</li>
                    <li>Seus dados serão removidos permanentemente</li>
                    <li>Você perderá acesso a todos os serviços</li>
                    <li>Esta ação não pode ser revertida</li>
                </ul>
                
                <p class="text-muted">
                    Se você tem certeza que deseja prosseguir, entre em contato com nosso suporte através do email 
                    <strong><EMAIL></strong> informando seu email de cadastro e o motivo do cancelamento.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <a href="mailto:<EMAIL>?subject=Solicitação de Cancelamento de Conta&body=Olá, gostaria de solicitar o cancelamento da minha conta (<?= e($user['email']) ?>)." 
                   class="btn btn-danger">
                    <i class="fas fa-envelope me-2"></i>
                    Entrar em Contato
                </a>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
// Validação do formulário
document.querySelector('form').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const currentPassword = document.getElementById('current_password').value;
    
    // Se uma nova senha foi fornecida
    if (newPassword) {
        // Verificar se a senha atual foi fornecida
        if (!currentPassword) {
            e.preventDefault();
            alert('Para alterar a senha, você deve fornecer sua senha atual.');
            document.getElementById('current_password').focus();
            return;
        }
        
        // Verificar se as senhas coincidem
        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('A confirmação da senha não confere.');
            document.getElementById('confirm_password').focus();
            return;
        }
        
        // Verificar tamanho mínimo
        if (newPassword.length < 6) {
            e.preventDefault();
            alert('A nova senha deve ter pelo menos 6 caracteres.');
            document.getElementById('new_password').focus();
            return;
        }
    }
});

// Limpar campos de senha se não estiver alterando
document.getElementById('new_password').addEventListener('input', function() {
    if (!this.value) {
        document.getElementById('current_password').value = '';
        document.getElementById('confirm_password').value = '';
    }
});
</script>
