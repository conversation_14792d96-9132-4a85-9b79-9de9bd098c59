<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - E1Copy AI Dashboard</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" onerror="this.onerror=null;this.href='<?= url('/assets/css/login.css') ?>';">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" onerror="this.remove();">
    <!-- CSS Local como fallback -->
    <noscript><link href="<?= url('/assets/css/login.css') ?>" rel="stylesheet"></noscript>
    
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            border: none;
            padding: 0.75rem;
            font-weight: 500;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #c82333 0%, #5a2d91 100%);
        }
        .alert {
            border-radius: 0.5rem;
            border: none;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border-right: none;
        }
        .form-control {
            border-left: none;
        }
        .form-control:focus {
            border-left: none;
        }
        .admin-badge {
            background: rgba(255,255,255,0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-card">
                    <div class="login-header admin">
                        <h3 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>
                            E1Copy AI
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">Área Administrativa</p>
                        <span class="admin-badge">
                            <i class="fas fa-crown me-1"></i>
                            Admin Access
                        </span>
                    </div>
                    
                    <div class="login-body">
                        <!-- Flash Messages -->
                        <?php if (isset($flash['error'])): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                <?= e($flash['error']) ?>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($flash['success'])): ?>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <?= e($flash['success']) ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="<?= url('/admin/login') ?>">
                            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email do Administrador</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user-shield"></i>
                                    </span>
                                    <input type="email" 
                                           class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                           id="email" 
                                           name="email" 
                                           value="<?= e($old['email'] ?? '') ?>"
                                           placeholder="<EMAIL>"
                                           required>
                                </div>
                                <?php if (isset($errors['email'])): ?>
                                    <div class="invalid-feedback d-block">
                                        <?= e($errors['email'][0]) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-4">
                                <label for="password" class="form-label">Senha</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-key"></i>
                                    </span>
                                    <input type="password" 
                                           class="form-control <?= isset($errors['password']) ? 'is-invalid' : '' ?>" 
                                           id="password" 
                                           name="password" 
                                           placeholder="Sua senha de administrador"
                                           required>
                                </div>
                                <?php if (isset($errors['password'])): ?>
                                    <div class="invalid-feedback d-block">
                                        <?= e($errors['password'][0]) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary admin btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Acessar Painel Admin
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                <i class="fas fa-lock me-1"></i>
                                Acesso restrito a administradores
                            </small>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="<?= url('/login') ?>" class="text-decoration-none">
                                <i class="fas fa-arrow-left me-1"></i>
                                Voltar ao login de cliente
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-3">
                    <small class="text-white-50">
                        © <?= date('Y') ?> E1Copy AI. Todos os direitos reservados.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" onerror="console.warn('Bootstrap JS não carregou')"></script>
    <!-- Script de fallback -->
    <script src="<?= url('/assets/js/fallback.js') ?>"></script>
</body>
</html>
