<?php
$title = 'Uso da IA - Relatórios - E1Copy AI';
$pageTitle = 'Relatório de Uso da IA';

// Buscar estatísticas
$db = Database::getInstance();

// Estatísticas gerais
$generalStats = $db->fetch("
    SELECT 
        COUNT(*) as total_generated,
        COUNT(CASE WHEN generation_status = 'completed' THEN 1 END) as successful,
        COUNT(CASE WHEN generation_status = 'failed' THEN 1 END) as failed,
        COUNT(CASE WHEN generation_status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN publish_status = 'published' THEN 1 END) as published,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as last_24h,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as last_7d,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as last_30d
    FROM generated_posts
");

// Estatísticas da fila
$queueStats = $db->fetch("
    SELECT 
        COUNT(*) as total_queue,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as queue_pending,
        COUNT(CASE WHEN status = 'processing' THEN 1 END) as queue_processing,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as queue_completed,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as queue_failed
    FROM content_queue
");

// Logs de execução recentes
$recentLogs = $db->fetchAll("
    SELECT el.*, u.name as user_name
    FROM ai_execution_logs el
    LEFT JOIN users u ON el.user_id = u.id
    ORDER BY el.created_at DESC
    LIMIT 50
");

// Posts gerados recentemente
$recentPosts = $db->fetchAll("
    SELECT gp.*, u.name as user_name
    FROM generated_posts gp
    LEFT JOIN users u ON gp.user_id = u.id
    ORDER BY gp.created_at DESC
    LIMIT 20
");

// Estatísticas por usuário
$userStats = $db->fetchAll("
    SELECT 
        u.name as user_name,
        u.email,
        COUNT(gp.id) as total_posts,
        COUNT(CASE WHEN gp.generation_status = 'completed' THEN 1 END) as successful_posts,
        COUNT(CASE WHEN gp.generation_status = 'failed' THEN 1 END) as failed_posts,
        COUNT(CASE WHEN gp.publish_status = 'published' THEN 1 END) as published_posts,
        MAX(gp.created_at) as last_generation
    FROM users u
    LEFT JOIN generated_posts gp ON u.id = gp.user_id
    WHERE u.role = 'client'
    GROUP BY u.id
    HAVING total_posts > 0
    ORDER BY total_posts DESC
    LIMIT 10
");

ob_start();
?>

<style>
.metric-card {
    border-left: 4px solid #007bff;
    transition: transform 0.2s;
}
.metric-card:hover {
    transform: translateY(-2px);
}
.log-entry {
    border-left: 3px solid #dee2e6;
    padding-left: 1rem;
    margin-bottom: 0.5rem;
}
.log-success { border-left-color: #28a745; }
.log-error { border-left-color: #dc3545; }
.log-warning { border-left-color: #ffc107; }
</style>

<!-- Métricas Principais -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card metric-card text-center">
            <div class="card-body">
                <h3 class="text-primary"><?= number_format($generalStats['total_generated']) ?></h3>
                <p class="text-muted mb-0">Total Gerados</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card text-center">
            <div class="card-body">
                <h3 class="text-success"><?= number_format($generalStats['successful']) ?></h3>
                <p class="text-muted mb-0">Sucessos</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card text-center">
            <div class="card-body">
                <h3 class="text-danger"><?= number_format($generalStats['failed']) ?></h3>
                <p class="text-muted mb-0">Falhas</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card text-center">
            <div class="card-body">
                <h3 class="text-info"><?= number_format($generalStats['published']) ?></h3>
                <p class="text-muted mb-0">Publicados</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card text-center">
            <div class="card-body">
                <h3 class="text-warning"><?= number_format($queueStats['queue_pending']) ?></h3>
                <p class="text-muted mb-0">Na Fila</p>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card metric-card text-center">
            <div class="card-body">
                <h3 class="text-secondary"><?= number_format($generalStats['last_24h']) ?></h3>
                <p class="text-muted mb-0">Últimas 24h</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Logs de Execução -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    Logs de Execução
                </h5>
                <button class="btn btn-primary btn-sm" onclick="processQueue()">
                    <i class="fas fa-play me-1"></i>
                    Processar Fila
                </button>
            </div>
            <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                <?php if (empty($recentLogs)): ?>
                    <p class="text-muted text-center">Nenhum log encontrado</p>
                <?php else: ?>
                    <?php foreach ($recentLogs as $log): ?>
                        <div class="log-entry log-<?= $log['status'] ?>">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong><?= ucfirst(str_replace('_', ' ', $log['action_type'])) ?></strong>
                                    <?php if ($log['user_name']): ?>
                                        <small class="text-muted">- <?= e($log['user_name']) ?></small>
                                    <?php endif; ?>
                                    <br>
                                    <small class="text-muted"><?= e($log['site_url']) ?></small>
                                    <?php if ($log['error_message']): ?>
                                        <br>
                                        <small class="text-danger"><?= e($log['error_message']) ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">
                                        <?= date('d/m H:i', strtotime($log['created_at'])) ?>
                                        <?php if ($log['execution_time_ms']): ?>
                                            <br><?= number_format($log['execution_time_ms']) ?>ms
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Posts Recentes -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    Posts Gerados Recentemente
                </h5>
            </div>
            <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                <?php if (empty($recentPosts)): ?>
                    <p class="text-muted text-center">Nenhum post encontrado</p>
                <?php else: ?>
                    <?php foreach ($recentPosts as $post): ?>
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong><?= e($post['post_title'] ?: 'Sem título') ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <?= e($post['user_name']) ?> - <?= e($post['site_name'] ?: $post['site_url']) ?>
                                    </small>
                                    <br>
                                    <span class="badge bg-<?= $post['generation_status'] === 'completed' ? 'success' : ($post['generation_status'] === 'failed' ? 'danger' : 'warning') ?>">
                                        <?= ucfirst($post['generation_status']) ?>
                                    </span>
                                    <?php if ($post['publish_status'] === 'published'): ?>
                                        <span class="badge bg-info">Publicado</span>
                                    <?php elseif ($post['publish_status'] === 'failed'): ?>
                                        <span class="badge bg-danger">Falha na Publicação</span>
                                    <?php endif; ?>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">
                                        <?= date('d/m H:i', strtotime($post['created_at'])) ?>
                                    </small>
                                    <?php if ($post['generation_status'] === 'completed' && $post['publish_status'] !== 'published'): ?>
                                        <br>
                                        <button class="btn btn-success btn-sm" onclick="publishPost(<?= $post['id'] ?>)">
                                            <i class="fas fa-upload"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Estatísticas por Usuário -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    Top Usuários por Geração de Conteúdo
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($userStats)): ?>
                    <p class="text-muted text-center">Nenhum dado encontrado</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Usuário</th>
                                    <th>Total Posts</th>
                                    <th>Sucessos</th>
                                    <th>Falhas</th>
                                    <th>Publicados</th>
                                    <th>Última Geração</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($userStats as $user): ?>
                                    <tr>
                                        <td>
                                            <strong><?= e($user['user_name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= e($user['email']) ?></small>
                                        </td>
                                        <td><span class="badge bg-primary"><?= $user['total_posts'] ?></span></td>
                                        <td><span class="badge bg-success"><?= $user['successful_posts'] ?></span></td>
                                        <td><span class="badge bg-danger"><?= $user['failed_posts'] ?></span></td>
                                        <td><span class="badge bg-info"><?= $user['published_posts'] ?></span></td>
                                        <td>
                                            <?php if ($user['last_generation']): ?>
                                                <?= date('d/m/Y H:i', strtotime($user['last_generation'])) ?>
                                            <?php else: ?>
                                                <span class="text-muted">Nunca</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
function processQueue() {
    if (confirm('Deseja processar a fila de geração de conteúdo?')) {
        fetch('<?= url('/api/content/process-queue') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                _token: '<?= $csrf_token ?>',
                max_items: 5
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Fila processada: ' + data.message);
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            alert('Erro na requisição: ' + error.message);
        });
    }
}

function publishPost(postId) {
    if (confirm('Deseja publicar este post no WordPress?')) {
        fetch('<?= url('/api/content/publish') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                _token: '<?= $csrf_token ?>',
                post_id: postId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Post publicado com sucesso!');
                location.reload();
            } else {
                alert('Erro: ' + data.message);
            }
        })
        .catch(error => {
            alert('Erro na requisição: ' + error.message);
        });
    }
}

// Auto-refresh a cada 30 segundos
setInterval(() => {
    location.reload();
}, 30000);
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
