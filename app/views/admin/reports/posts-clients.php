<?php
$title = 'Posts Clients - Relatórios - E1Copy AI';
$pageTitle = 'Posts Clients';
ob_start();

// Verificar se o sistema de IA está instalado
$aiSystemInstalled = isset($aiSystemInstalled) ? $aiSystemInstalled : true;
?>

<style>
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}
.status-success { background-color: #d4edda; color: #155724; }
.status-error { background-color: #f8d7da; color: #721c24; }
.status-warning { background-color: #fff3cd; color: #856404; }
.status-pending { background-color: #cce7ff; color: #004085; }
.status-info { background-color: #e2e3e5; color: #383d41; }

.stats-card {
    border-left: 4px solid #007bff;
    transition: transform 0.2s;
}
.stats-card:hover {
    transform: translateY(-2px);
}
</style>

<?php if (!$aiSystemInstalled): ?>
    <div class="alert alert-warning">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>Sistema de IA Não Instalado</h6>
        <p class="mb-2">
            As funcionalidades de geração de conteúdo com IA não estão disponíveis.
            Para habilitar essas funcionalidades, execute o arquivo <code>install-ai-system.sql</code> no seu banco de dados.
        </p>
        <a href="<?= url('/admin/ai-settings') ?>" class="btn btn-warning btn-sm">
            <i class="fas fa-cog me-1"></i>
            Ver Instruções de Instalação
        </a>
    </div>
<?php endif; ?>

<!-- Estatísticas Gerais -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-primary"><?= number_format($generalStats['total_clients']) ?></h3>
                <p class="text-muted mb-0">Total de Clientes</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-info"><?= number_format($generalStats['total_sites']) ?></h3>
                <p class="text-muted mb-0">Sites Cadastrados</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-success"><?= number_format($generalStats['total_posts']) ?></h3>
                <p class="text-muted mb-0">Posts Gerados</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-warning"><?= number_format($pendingPostsData['summary']['total_posts'] ?? $pendingPostsData['total_posts'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Posts Pendentes</p>
                <small class="text-muted">
                    <?= isset($pendingPostsData['timestamp']) ? 'Atualizado: ' . date('H:i', strtotime($pendingPostsData['timestamp'])) : '' ?>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Filtros e Busca -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">Buscar</label>
                <input type="text" class="form-control" id="search" name="search" 
                       placeholder="Nome, email, site..." value="<?= e($search) ?>">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">Todos</option>
                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Ativos</option>
                    <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inativos</option>
                    <option value="has_errors" <?= $status === 'has_errors' ? 'selected' : '' ?>>Com Erros</option>
                    <option value="has_pending" <?= $status === 'has_pending' ? 'selected' : '' ?>>Com Pendências</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="order_by" class="form-label">Ordenar por</label>
                <select class="form-select" id="order_by" name="order_by">
                    <option value="last_activity" <?= $orderBy === 'last_activity' ? 'selected' : '' ?>>Última Atividade</option>
                    <option value="site_name" <?= $orderBy === 'site_name' ? 'selected' : '' ?>>Nome do Site</option>
                    <option value="user_name" <?= $orderBy === 'user_name' ? 'selected' : '' ?>>Nome do Cliente</option>
                    <option value="total_posts" <?= $orderBy === 'total_posts' ? 'selected' : '' ?>>Total de Posts</option>
                    <option value="pending_posts" <?= $orderBy === 'pending_posts' ? 'selected' : '' ?>>Posts Pendentes</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="order_dir" class="form-label">Direção</label>
                <select class="form-select" id="order_dir" name="order_dir">
                    <option value="desc" <?= $orderDir === 'desc' ? 'selected' : '' ?>>Decrescente</option>
                    <option value="asc" <?= $orderDir === 'asc' ? 'selected' : '' ?>>Crescente</option>
                </select>
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>
                    Filtrar
                </button>
                <a href="<?= url('/admin/reports/posts-clients') ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    Limpar
                </a>
                <button type="button" class="btn btn-success ms-2" onclick="syncAllSites()">
                    <i class="fas fa-sync me-1"></i>
                    Sincronizar Todos
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Posts Pendentes em Tempo Real -->
<?php
$totalPendingPosts = $pendingPostsData['summary']['total_posts'] ?? $pendingPostsData['total_posts'] ?? 0;
if (isset($pendingPostsData) && $totalPendingPosts > 0): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-clock me-2"></i>
            Posts Pendentes para Processamento (<?= $totalPendingPosts ?>)
        </h5>
        <small class="text-muted">
            Última atualização: <?= date('d/m/Y H:i:s', strtotime($pendingPostsData['timestamp'])) ?>
        </small>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Site</th>
                        <th>Título</th>
                        <th>Tipo</th>
                        <th>Criado em</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $displayPosts = [];
                    if (isset($pendingPostsData['posts_by_site'])) {
                        // Nova estrutura - posts organizados por site
                        foreach ($pendingPostsData['posts_by_site'] as $siteData) {
                            foreach ($siteData['posts'] as $post) {
                                $post['site_info'] = $siteData['site_info'];
                                $displayPosts[] = $post;
                            }
                        }
                    } elseif (isset($pendingPostsData['all_posts'])) {
                        // Estrutura de compatibilidade
                        $displayPosts = $pendingPostsData['all_posts'];
                    }

                    foreach (array_slice($displayPosts, 0, 10) as $post): ?>
                        <tr>
                            <td>
                                <small>
                                    <strong><?= e($post['site_info']['site_name'] ?? $post['site_name'] ?? 'Site desconhecido') ?></strong><br>
                                    <span class="text-muted"><?= e($post['site_info']['user_name'] ?? $post['user_name'] ?? 'Usuário desconhecido') ?></span>
                                </small>
                            </td>
                            <td>
                                <small><?= e(substr($post['title'], 0, 50)) ?><?= strlen($post['title']) > 50 ? '...' : '' ?></small>
                            </td>
                            <td>
                                <span class="badge bg-info"><?= e($post['post_type']) ?></span>
                            </td>
                            <td>
                                <small><?= date('d/m H:i', strtotime($post['created_at'])) ?></small>
                            </td>
                            <td>
                                <span class="badge bg-warning">Pendente</span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php if ($totalPendingPosts > 10): ?>
            <div class="text-center">
                <small class="text-muted">
                    Mostrando 10 de <?= $totalPendingPosts ?> posts pendentes
                </small>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>


<!-- Lista de Sites/Clientes -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            Sites e Estatísticas de Posts
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($sites)): ?>
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h6>Nenhum site encontrado</h6>
                <p class="text-muted">Não há sites cadastrados ou que correspondam aos filtros aplicados.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Status</th>
                            <th>Cliente</th>
                            <th>Site</th>
                            <th>Posts</th>
                            <th>Pendentes</th>
                            <th>Status API</th>
                            <th>Última Atividade</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sites as $site): ?>
                            <tr>
                                <td>
                                    <?php
                                    $statusClass = 'status-' . $site['overall_status'];
                                    $statusText = '';
                                    switch ($site['overall_status']) {
                                        case 'success': $statusText = 'Ativo'; break;
                                        case 'error': $statusText = 'Erro'; break;
                                        case 'warning': $statusText = 'Atenção'; break;
                                        case 'pending': $statusText = 'Pendente'; break;
                                        default: $statusText = 'Neutro'; break;
                                    }
                                    ?>
                                    <span class="badge status-badge <?= $statusClass ?>"><?= $statusText ?></span>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= e($site['user_name']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= e($site['user_email']) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= e($site['site_name'] ?: 'Site sem nome') ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <a href="<?= e($site['site_url']) ?>" target="_blank" class="text-decoration-none">
                                                <?= e($site['site_url']) ?>
                                                <i class="fas fa-external-link-alt ms-1"></i>
                                            </a>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <div class="fw-bold text-primary"><?= number_format($site['total_posts']) ?></div>
                                        <small class="text-muted">
                                            <?= $site['published_posts'] ?> publicados
                                            <?php if ($site['failed_posts'] > 0): ?>
                                                <br><span class="text-danger"><?= $site['failed_posts'] ?> falharam</span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <?php if (isset($site['pending_posts']) && $site['pending_posts'] > 0): ?>
                                            <span class="badge bg-warning"><?= $site['pending_posts'] ?> pendentes</span>
                                        <?php else: ?>
                                            <span class="text-muted">0</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if (isset($site['api_status'])): ?>
                                        <div>
                                            <?php
                                            $statusBadge = '';
                                            switch ($site['api_status']) {
                                                case 'success':
                                                    $statusBadge = '<span class="badge bg-success">Conectado</span>';
                                                    break;
                                                case 'error':
                                                    $statusBadge = '<span class="badge bg-danger">Erro</span>';
                                                    break;
                                                case 'not_found':
                                                    $statusBadge = '<span class="badge bg-secondary">Não encontrado</span>';
                                                    break;
                                                default:
                                                    $statusBadge = '<span class="badge bg-warning">Desconhecido</span>';
                                            }
                                            echo $statusBadge;
                                            ?>

                                            <?php if (isset($site['api_error']) && $site['api_error']): ?>
                                                <br>
                                                <small class="text-danger" title="<?= e($site['api_error']) ?>">
                                                    <?= e(substr($site['api_error'], 0, 50)) ?><?= strlen($site['api_error']) > 50 ? '...' : '' ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Não verificado</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($site['last_activity'] && $site['last_activity'] !== '1970-01-01 00:00:00'): ?>
                                        <div>
                                            <?= date('d/m/Y', strtotime($site['last_activity'])) ?>
                                            <br>
                                            <small class="text-muted"><?= date('H:i', strtotime($site['last_activity'])) ?></small>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Nunca</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= url("/admin/clients/{$site['user_id']}") ?>"
                                           class="btn btn-outline-primary" title="Ver Cliente">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($site['site_id']): ?>
                                            <button class="btn btn-outline-info"
                                                    onclick="syncSite(<?= $site['site_id'] ?>)"
                                                    title="Sincronizar Site">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                            <button class="btn btn-outline-success"
                                                    onclick="generatePost(<?= $site['site_id'] ?>)"
                                                    title="Gerar Post">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function generatePost(siteId) {
    if (confirm('Deseja adicionar um novo post à fila de geração para este site?')) {
        // Implementar chamada AJAX para adicionar à fila
        console.log('Gerando post para site:', siteId);
        // TODO: Implementar
    }
}

function syncAllSites() {
    if (confirm('Deseja sincronizar todos os sites? Isso pode levar alguns minutos.')) {
        const btn = event.target;
        const originalText = btn.innerHTML;

        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Sincronizando...';

        fetch('<?= url('/api/sites/sync-all') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                _token: '<?= $csrf_token ?? '' ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ Erro: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Erro na requisição: ' + error.message);
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
    }
}

function syncSite(siteId) {
    if (confirm('Deseja sincronizar este site?')) {
        const btn = event.target;
        const originalText = btn.innerHTML;

        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        fetch(`<?= url('/api/sites/sync') ?>/${siteId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                _token: '<?= $csrf_token ?? '' ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ Erro: ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ Erro na requisição: ' + error.message);
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = originalText;
        });
    }
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
