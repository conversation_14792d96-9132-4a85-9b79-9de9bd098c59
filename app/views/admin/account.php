<?php
$title = 'Minha Conta Admin - E1Copy AI';
$pageTitle = 'Minha Conta';
ob_start();
?>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-shield me-2"></i>
                    Informações da Conta de Administrador
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= url('/admin/account') ?>">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Nome Completo</label>
                            <input type="text" 
                                   class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
                                   id="name" 
                                   name="name" 
                                   value="<?= e($old['name'] ?? $user['name']) ?>"
                                   required>
                            <?php if (isset($errors['name'])): ?>
                                <div class="invalid-feedback">
                                    <?= e($errors['name'][0]) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" 
                                   class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                   id="email" 
                                   name="email" 
                                   value="<?= e($old['email'] ?? $user['email']) ?>"
                                   required>
                            <?php if (isset($errors['email'])): ?>
                                <div class="invalid-feedback">
                                    <?= e($errors['email'][0]) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6 class="mb-3">
                        <i class="fas fa-lock me-2"></i>
                        Alterar Senha (opcional)
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="current_password" class="form-label">Senha Atual</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="current_password" 
                                   name="current_password"
                                   placeholder="Digite sua senha atual">
                            <div class="form-text">Necessário apenas se quiser alterar a senha</div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="new_password" class="form-label">Nova Senha</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="new_password" 
                                   name="new_password"
                                   placeholder="Nova senha (mín. 8 caracteres)">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="confirm_password" class="form-label">Confirmar Nova Senha</label>
                            <input type="password" 
                                   class="form-control" 
                                   id="confirm_password" 
                                   name="confirm_password"
                                   placeholder="Confirme a nova senha">
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-muted small">
                            <i class="fas fa-info-circle me-1"></i>
                            Conta criada em: <?= date('d/m/Y H:i', strtotime($user['created_at'])) ?>
                        </div>
                        
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Salvar Alterações
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Informações Adicionais -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    Informações de Segurança
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>Tipo de Conta:</strong><br>
                        <span class="badge bg-danger fs-6">
                            <i class="fas fa-crown me-1"></i>
                            Administrador
                        </span>
                    </div>
                    
                    <div class="col-md-6">
                        <strong>Status da Conta:</strong><br>
                        <span class="badge bg-<?= $user['status'] === 'active' ? 'success' : 'danger' ?> fs-6">
                            <?= ucfirst($user['status']) ?>
                        </span>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <strong>Email Verificado:</strong><br>
                        <?php if ($user['email_verified_at']): ?>
                            <span class="text-success">
                                <i class="fas fa-check-circle me-1"></i>
                                Verificado em <?= date('d/m/Y', strtotime($user['email_verified_at'])) ?>
                            </span>
                        <?php else: ?>
                            <span class="text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Não verificado
                            </span>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-6">
                        <strong>Última Atualização:</strong><br>
                        <?= date('d/m/Y H:i', strtotime($user['updated_at'])) ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Atividade Recente -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    Atividade Recente
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Funcionalidade em desenvolvimento:</strong> 
                    Em breve você poderá visualizar suas atividades recentes no sistema.
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Sessão Atual</h6>
                        <ul class="list-unstyled">
                            <li><strong>IP:</strong> <?= $_SERVER['REMOTE_ADDR'] ?? 'N/A' ?></li>
                            <li><strong>Navegador:</strong> <?= substr($_SERVER['HTTP_USER_AGENT'] ?? 'N/A', 0, 50) ?>...</li>
                            <li><strong>Login:</strong> <?= isset($_SESSION['login_time']) ? date('d/m/Y H:i', $_SESSION['login_time']) : 'N/A' ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Permissões</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-1"></i> Gerenciar clientes</li>
                            <li><i class="fas fa-check text-success me-1"></i> Configurar sistema</li>
                            <li><i class="fas fa-check text-success me-1"></i> Visualizar relatórios</li>
                            <li><i class="fas fa-check text-success me-1"></i> Gerenciar APIs</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Configurações de Segurança -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lock me-2"></i>
                    Configurações de Segurança
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Autenticação de Dois Fatores</h6>
                        <p class="text-muted">Adicione uma camada extra de segurança à sua conta.</p>
                        <button type="button" class="btn btn-outline-primary" disabled>
                            <i class="fas fa-mobile-alt me-1"></i>
                            Configurar 2FA (Em breve)
                        </button>
                    </div>
                    <div class="col-md-6">
                        <h6>Sessões Ativas</h6>
                        <p class="text-muted">Gerencie suas sessões ativas em diferentes dispositivos.</p>
                        <button type="button" class="btn btn-outline-warning" disabled>
                            <i class="fas fa-sign-out-alt me-1"></i>
                            Encerrar Outras Sessões (Em breve)
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
// Validação do formulário
document.querySelector('form').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const currentPassword = document.getElementById('current_password').value;
    
    // Se uma nova senha foi fornecida
    if (newPassword) {
        // Verificar se a senha atual foi fornecida
        if (!currentPassword) {
            e.preventDefault();
            alert('Para alterar a senha, você deve fornecer sua senha atual.');
            document.getElementById('current_password').focus();
            return;
        }
        
        // Verificar se as senhas coincidem
        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('A confirmação da senha não confere.');
            document.getElementById('confirm_password').focus();
            return;
        }
        
        // Verificar tamanho mínimo (mais rigoroso para admin)
        if (newPassword.length < 8) {
            e.preventDefault();
            alert('A nova senha deve ter pelo menos 8 caracteres.');
            document.getElementById('new_password').focus();
            return;
        }
        
        // Verificar complexidade da senha
        const hasUpperCase = /[A-Z]/.test(newPassword);
        const hasLowerCase = /[a-z]/.test(newPassword);
        const hasNumbers = /\d/.test(newPassword);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(newPassword);
        
        if (!hasUpperCase || !hasLowerCase || !hasNumbers) {
            e.preventDefault();
            alert('A senha deve conter pelo menos: 1 letra maiúscula, 1 minúscula e 1 número.');
            document.getElementById('new_password').focus();
            return;
        }
    }
});

// Limpar campos de senha se não estiver alterando
document.getElementById('new_password').addEventListener('input', function() {
    if (!this.value) {
        document.getElementById('current_password').value = '';
        document.getElementById('confirm_password').value = '';
    }
});

// Indicador de força da senha
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strengthIndicator = document.getElementById('password-strength') || createStrengthIndicator();
    
    if (password.length === 0) {
        strengthIndicator.style.display = 'none';
        return;
    }
    
    strengthIndicator.style.display = 'block';
    
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
    
    const colors = ['danger', 'danger', 'warning', 'info', 'success'];
    const texts = ['Muito fraca', 'Fraca', 'Regular', 'Boa', 'Forte'];
    
    strengthIndicator.className = `alert alert-${colors[strength - 1]} mt-2`;
    strengthIndicator.textContent = `Força da senha: ${texts[strength - 1]}`;
});

function createStrengthIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'password-strength';
    indicator.style.display = 'none';
    document.getElementById('new_password').parentNode.appendChild(indicator);
    return indicator;
}
</script>
