<?php
$title = 'Gerenciar Planos - E1Copy AI';
$pageTitle = 'Meus Planos';
ob_start();
?>

<!-- Lista de Planos -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-credit-card me-2"></i>
            Planos Disponíveis (<?= count($plans) ?>)
        </h5>
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addPlanModal">
            <i class="fas fa-plus me-1"></i>
            Novo Plano
        </button>
    </div>
    <div class="card-body">
        <?php if (!empty($plans)): ?>
            <div class="row">
                <?php foreach ($plans as $plan): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 <?= $plan['status'] === 'active' ? 'border-success' : 'border-secondary' ?>">
                            <div class="card-header text-center">
                                <h5 class="card-title"><?= e($plan['name']) ?></h5>
                                <div class="display-6 text-primary">
                                    R$ <?= number_format($plan['price'], 2, ',', '.') ?>
                                </div>
                                <small class="text-muted">
                                    <?php
                                    switch($plan['billing_cycle']) {
                                        case 'monthly': echo 'por mês'; break;
                                        case 'yearly': echo 'por ano'; break;
                                        case 'lifetime': echo 'vitalício'; break;
                                        default: echo $plan['billing_cycle'];
                                    }
                                    ?>
                                </small>
                            </div>
                            <div class="card-body">
                                <p class="card-text"><?= e($plan['description']) ?></p>
                                
                                <?php if ($plan['features']): ?>
                                    <?php $features = json_decode($plan['features'], true); ?>
                                    <ul class="list-unstyled">
                                        <?php foreach ($features as $feature): ?>
                                            <li class="mb-1">
                                                <i class="fas fa-check text-success me-2"></i>
                                                <?= e($feature) ?>
                                            </li>
                                        <?php endforeach; ?>
                                    </ul>
                                <?php endif; ?>
                                
                                <hr>
                                
                                <div class="row text-center">
                                    <div class="col-6">
                                        <strong>Limite Mensal</strong><br>
                                        <small><?= $plan['limits_per_month'] ? number_format($plan['limits_per_month']) : 'Ilimitado' ?></small>
                                    </div>
                                    <div class="col-6">
                                        <strong>Sites</strong><br>
                                        <small><?= $plan['max_sites'] ?? 'Ilimitado' ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-<?= $plan['status'] === 'active' ? 'success' : 'secondary' ?> fs-6">
                                        <?= ucfirst($plan['status']) ?>
                                    </span>
                                    
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                                onclick="editPlan(<?= $plan['id'] ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form method="POST" action="<?= url("/admin/plans/{$plan['id']}") ?>" class="d-inline">
                                            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <button type="submit" class="btn btn-outline-<?= $plan['status'] === 'active' ? 'warning' : 'success' ?> btn-sm">
                                                <i class="fas fa-<?= $plan['status'] === 'active' ? 'pause' : 'play' ?>"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-credit-card fa-3x text-muted mb-3"></i>
                <h5>Nenhum plano cadastrado</h5>
                <p class="text-muted">Crie o primeiro plano para começar a oferecer seus serviços.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal para adicionar plano -->
<div class="modal fade" id="addPlanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Criar Novo Plano</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="<?= url('/admin/plans/create') ?>">
                <div class="modal-body">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="planName" class="form-label">Nome do Plano</label>
                            <input type="text" class="form-control" id="planName" name="name" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="planPrice" class="form-label">Preço (R$)</label>
                            <input type="number" class="form-control" id="planPrice" name="price" 
                                   step="0.01" min="0" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="planBilling" class="form-label">Ciclo de Cobrança</label>
                            <select class="form-select" id="planBilling" name="billing_cycle" required>
                                <option value="monthly">Mensal</option>
                                <option value="yearly">Anual</option>
                                <option value="lifetime">Vitalício</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="planLimit" class="form-label">Limite Mensal</label>
                            <input type="number" class="form-control" id="planLimit" name="limits_per_month" 
                                   placeholder="Deixe vazio para ilimitado">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="planSites" class="form-label">Máximo de Sites</label>
                            <input type="number" class="form-control" id="planSites" name="max_sites" 
                                   placeholder="Deixe vazio para ilimitado">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="planDescription" class="form-label">Descrição</label>
                        <textarea class="form-control" id="planDescription" name="description" 
                                  rows="3" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Funcionalidades</label>
                        <div id="featuresContainer">
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" name="features[]" 
                                       placeholder="Ex: Geração de conteúdo com IA">
                                <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addFeature()">
                            <i class="fas fa-plus me-1"></i>
                            Adicionar Funcionalidade
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-success">Criar Plano</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para editar plano -->
<div class="modal fade" id="editPlanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Editar Plano</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editPlanForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                    <input type="hidden" name="action" value="update">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="editPlanName" class="form-label">Nome do Plano</label>
                            <input type="text" class="form-control" id="editPlanName" name="name" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="editPlanPrice" class="form-label">Preço (R$)</label>
                            <input type="number" class="form-control" id="editPlanPrice" name="price" 
                                   step="0.01" min="0" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="editPlanBilling" class="form-label">Ciclo de Cobrança</label>
                            <select class="form-select" id="editPlanBilling" name="billing_cycle" required>
                                <option value="monthly">Mensal</option>
                                <option value="yearly">Anual</option>
                                <option value="lifetime">Vitalício</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="editPlanLimit" class="form-label">Limite Mensal</label>
                            <input type="number" class="form-control" id="editPlanLimit" name="limits_per_month" 
                                   placeholder="Deixe vazio para ilimitado">
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="editPlanSites" class="form-label">Máximo de Sites</label>
                            <input type="number" class="form-control" id="editPlanSites" name="max_sites" 
                                   placeholder="Deixe vazio para ilimitado">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editPlanDescription" class="form-label">Descrição</label>
                        <textarea class="form-control" id="editPlanDescription" name="description" 
                                  rows="3" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Funcionalidades</label>
                        <div id="editFeaturesContainer">
                            <!-- Features serão carregadas via JavaScript -->
                        </div>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEditFeature()">
                            <i class="fas fa-plus me-1"></i>
                            Adicionar Funcionalidade
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>

<script>
// Adicionar funcionalidade
function addFeature() {
    const container = document.getElementById('featuresContainer');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="features[]" placeholder="Ex: Suporte prioritário">
        <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeFeature(button) {
    button.closest('.input-group').remove();
}

// Editar plano
function editPlan(planId) {
    // Buscar dados do plano
    const planData = <?= json_encode($plans) ?>.find(p => p.id == planId);
    
    if (planData) {
        document.getElementById('editPlanName').value = planData.name;
        document.getElementById('editPlanPrice').value = planData.price;
        document.getElementById('editPlanBilling').value = planData.billing_cycle;
        document.getElementById('editPlanLimit').value = planData.limits_per_month || '';
        document.getElementById('editPlanSites').value = planData.max_sites || '';
        document.getElementById('editPlanDescription').value = planData.description;
        
        // Carregar funcionalidades
        const featuresContainer = document.getElementById('editFeaturesContainer');
        featuresContainer.innerHTML = '';
        
        if (planData.features) {
            const features = JSON.parse(planData.features);
            features.forEach(feature => {
                addEditFeatureWithValue(feature);
            });
        }
        
        // Definir action do form
        document.getElementById('editPlanForm').action = `<?= url('/admin/plans/') ?>${planId}`;
        
        // Mostrar modal
        new bootstrap.Modal(document.getElementById('editPlanModal')).show();
    }
}

function addEditFeature() {
    addEditFeatureWithValue('');
}

function addEditFeatureWithValue(value) {
    const container = document.getElementById('editFeaturesContainer');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="features[]" value="${value}" placeholder="Ex: Suporte prioritário">
        <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(div);
}

// Confirmação para alteração de status
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(e) {
        const action = this.querySelector('input[name="action"]')?.value;
        
        if (action === 'toggle_status') {
            if (!confirm('Tem certeza que deseja alterar o status deste plano?')) {
                e.preventDefault();
            }
        }
    });
});
</script>
