<?php
ob_start();
$title = 'Documentação da API - E1Copy AI';
?>

<style>
.api-endpoint {
    background: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 5px 5px 0;
}

.api-endpoint h5 {
    color: #007bff;
    margin-bottom: 10px;
}

.method-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 3px;
    font-weight: bold;
    margin-right: 10px;
}

.method-get { background: #28a745; color: white; }
.method-post { background: #007bff; color: white; }
.method-put { background: #ffc107; color: black; }
.method-delete { background: #dc3545; color: white; }

.code-block {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}
</style>

<!-- Cabe<PERSON>lho -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="fas fa-book me-2"></i>
            Documentação da API
        </h1>
        <p class="text-muted mb-0">Guia completo da API E1Copy AI Dashboard</p>
    </div>
    <div>
        <span class="badge bg-success">v1.0</span>
    </div>
</div>

<!-- Estatísticas da API -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-primary"><?= number_format($apiStats['total_requests'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Requisições (30 dias)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-info"><?= number_format($apiStats['unique_users'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Usuários Únicos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-success"><?= number_format($apiStats['successful_requests'] ?? 0) ?></h3>
                <p class="text-muted mb-0">Sucessos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="text-warning"><?= number_format(($apiStats['avg_response_time'] ?? 0), 0) ?>ms</h3>
                <p class="text-muted mb-0">Tempo Médio</p>
            </div>
        </div>
    </div>
</div>

<!-- Visão Geral -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-info-circle me-2"></i>
            Visão Geral
        </h5>
    </div>
    <div class="card-body">
        <p>A API E1Copy AI Dashboard fornece endpoints para integração com o plugin WordPress e sistemas externos. Todas as requisições requerem autenticação via chave de API.</p>
        
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-server me-2"></i>URL Base</h6>
                <div class="code-block">
<?= url('') ?>
                </div>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-key me-2"></i>Autenticação</h6>
                <div class="code-block">
X-E1Copy-API-Key: e1copy_...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fluxo de Funcionamento -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-flow-chart me-2"></i>
            Fluxo de Funcionamento
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-12">
                <ol class="list-group list-group-numbered">
                    <li class="list-group-item">
                        <strong>Cliente cadastra site</strong> no dashboard e recebe chave de licença
                    </li>
                    <li class="list-group-item">
                        <strong>Plugin WordPress</strong> é configurado com a chave de licença
                    </li>
                    <li class="list-group-item">
                        <strong>Plugin cria posts pendentes</strong> na tabela local (processed=0)
                    </li>
                    <li class="list-group-item">
                        <strong>Dashboard coleta posts</strong> via API <code>/wp-json/e1copy-ai/v1/products?processed=0</code>
                    </li>
                    <li class="list-group-item">
                        <strong>Sistema N8N consome</strong> a API <code>/api/v1/pending-posts</code> do dashboard
                    </li>
                    <li class="list-group-item">
                        <strong>Groq API gera conteúdo</strong> e publica de volta no WordPress
                    </li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Endpoints Principais -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-plug me-2"></i>
            Endpoints Principais
        </h5>
    </div>
    <div class="card-body">
        
        <!-- Posts Pendentes -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-get">GET</span>
                /api/v1/pending-posts
            </h5>
            <p><strong>Descrição:</strong> Lista todos os posts pendentes de todos os sites conectados</p>
            <p><strong>Autenticação:</strong> Não requerida (endpoint interno)</p>
            
            <h6>Resposta de Sucesso:</h6>
            <div class="code-block">
{
  "success": true,
  "data": {
    "total_sites": 5,
    "total_posts": 23,
    "posts": [
      {
        "id": 123,
        "title": "Título do Post",
        "post_type": "product",
        "created_at": "2024-01-15 10:30:00",
        "processed": 0,
        "site_info": {
          "site_id": 1,
          "site_name": "Meu Site",
          "site_url": "https://meusite.com",
          "user_name": "João Silva",
          "user_email": "<EMAIL>"
        }
      }
    ],
    "sites_stats": [
      {
        "site_id": 1,
        "site_name": "Meu Site",
        "total_posts": 5,
        "status": "success"
      }
    ],
    "timestamp": "2024-01-15 12:00:00"
  }
}
            </div>
        </div>

        <!-- Verificar Chave -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-post">POST</span>
                /api/v1/verify-key
            </h5>
            <p><strong>Descrição:</strong> Verifica se uma chave de API é válida</p>
            <p><strong>Autenticação:</strong> Chave de API no corpo da requisição</p>
            
            <h6>Parâmetros:</h6>
            <div class="code-block">
{
  "api_key": "e1copy_80e4fb65c87c3362fcee6d4948a0fb525a999dc6bbcd4a792c9ece08d4cb903f"
}
            </div>
            
            <h6>Resposta de Sucesso:</h6>
            <div class="code-block">
{
  "success": true,
  "data": {
    "valid": true,
    "user_id": 123,
    "plan": "Premium",
    "status": "active"
  }
}
            </div>
        </div>

        <!-- Status do Cliente -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-get">GET</span>
                /api/v1/client-status/{key}
            </h5>
            <p><strong>Descrição:</strong> Obtém informações detalhadas sobre o status de um cliente</p>
            <p><strong>Autenticação:</strong> Chave de API na URL</p>
            
            <h6>Resposta de Sucesso:</h6>
            <div class="code-block">
{
  "success": true,
  "data": {
    "user_id": 123,
    "name": "João Silva",
    "email": "<EMAIL>",
    "plan": "Premium",
    "status": "active",
    "subscription_ends": "2024-12-31",
    "monthly_usage": 150,
    "monthly_limit": 1000
  }
}
            </div>
        </div>

        <!-- Registrar Site -->
        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-post">POST</span>
                /api/v1/register-site
            </h5>
            <p><strong>Descrição:</strong> Registra um site WordPress no sistema</p>
            <p><strong>Autenticação:</strong> Chave de API no corpo da requisição</p>
            
            <h6>Parâmetros:</h6>
            <div class="code-block">
{
  "api_key": "e1copy_...",
  "site_url": "https://meusite.com",
  "plugin_version": "1.0.0"
}
            </div>
        </div>

    </div>
</div>

<!-- Endpoints do Plugin WordPress -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fab fa-wordpress me-2"></i>
            Endpoints do Plugin WordPress
        </h5>
    </div>
    <div class="card-body">
        <p>Estes são os endpoints que o plugin WordPress expõe e que o dashboard consome:</p>

        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-get">GET</span>
                /wp-json/e1copy-ai/v1/products?processed=0
            </h5>
            <p><strong>Descrição:</strong> Lista posts de produtos não processados</p>
            <p><strong>Autenticação:</strong> Header <code>X-E1Copy-API-Key</code></p>

            <h6>Exemplo de Requisição:</h6>
            <div class="code-block">
curl -X GET "https://meusite.com/wp-json/e1copy-ai/v1/products?processed=0" \
     -H "X-E1Copy-API-Key: e1copy_80e4fb65c87c3362fcee6d4948a0fb525a999dc6bbcd4a792c9ece08d4cb903f"
            </div>
        </div>

        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-get">GET</span>
                /wp-json/e1copy-ai/v1/posts
            </h5>
            <p><strong>Descrição:</strong> Lista todos os posts do tipo "Blog Tradicional"</p>
            <p><strong>Parâmetros:</strong> <code>per_page</code>, <code>page</code>, <code>status</code>, <code>processed</code></p>
        </div>

        <div class="api-endpoint">
            <h5>
                <span class="method-badge method-get">GET</span>
                /wp-json/e1copy-ai/v1/verify-key
            </h5>
            <p><strong>Descrição:</strong> Verifica se a chave de API é válida no plugin</p>
        </div>
    </div>
</div>

<!-- Códigos de Resposta -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-code me-2"></i>
            Códigos de Resposta
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Código</th>
                        <th>Status</th>
                        <th>Descrição</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="badge bg-success">200</span></td>
                        <td>OK</td>
                        <td>Requisição processada com sucesso</td>
                    </tr>
                    <tr>
                        <td><span class="badge bg-warning">400</span></td>
                        <td>Bad Request</td>
                        <td>Parâmetros obrigatórios não fornecidos</td>
                    </tr>
                    <tr>
                        <td><span class="badge bg-danger">401</span></td>
                        <td>Unauthorized</td>
                        <td>Chave de API inválida ou expirada</td>
                    </tr>
                    <tr>
                        <td><span class="badge bg-danger">403</span></td>
                        <td>Forbidden</td>
                        <td>Acesso negado ou conta suspensa</td>
                    </tr>
                    <tr>
                        <td><span class="badge bg-warning">404</span></td>
                        <td>Not Found</td>
                        <td>Recurso não encontrado</td>
                    </tr>
                    <tr>
                        <td><span class="badge bg-danger">500</span></td>
                        <td>Internal Server Error</td>
                        <td>Erro interno do servidor</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Endpoints Mais Utilizados -->
<?php if (!empty($popularEndpoints)): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-chart-bar me-2"></i>
            Endpoints Mais Utilizados (30 dias)
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Endpoint</th>
                        <th>Requisições</th>
                        <th>Tempo Médio</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($popularEndpoints as $endpoint): ?>
                        <tr>
                            <td><code><?= e($endpoint['endpoint']) ?></code></td>
                            <td><?= number_format($endpoint['requests']) ?></td>
                            <td><?= number_format($endpoint['avg_response_time'], 0) ?>ms</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Exemplos de Integração -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-code-branch me-2"></i>
            Exemplos de Integração
        </h5>
    </div>
    <div class="card-body">

        <h6>N8N - Buscar Posts Pendentes</h6>
        <div class="code-block">
// Configuração do nó HTTP Request no N8N
URL: <?= url('/api/v1/pending-posts') ?>

Method: GET
Headers: {
  "Content-Type": "application/json"
}

// O retorno pode ser processado para:
// 1. Filtrar posts por site
// 2. Enviar para processamento de IA
// 3. Gerar conteúdo via Groq
// 4. Publicar de volta no WordPress
        </div>

        <h6>cURL - Verificar Status do Cliente</h6>
        <div class="code-block">
curl -X GET "<?= url('/api/v1/client-status/e1copy_...') ?>" \
     -H "Content-Type: application/json"
        </div>

        <h6>JavaScript - Registrar Site</h6>
        <div class="code-block">
fetch('<?= url('/api/v1/register-site') ?>', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    api_key: 'e1copy_...',
    site_url: 'https://meusite.com',
    plugin_version: '1.0.0'
  })
})
.then(response => response.json())
.then(data => console.log(data));
        </div>
    </div>
</div>

<!-- Notas Importantes -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Notas Importantes
        </h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <h6><i class="fas fa-info-circle me-2"></i>Mudanças Recentes</h6>
            <ul class="mb-0">
                <li>A opção "Configurações da API REST" foi removida do plugin</li>
                <li>Agora a autenticação usa exclusivamente as chaves geradas no painel de cliente</li>
                <li>O endpoint <code>/api/v1/pending-posts</code> agrega dados de todos os sites automaticamente</li>
            </ul>
        </div>

        <div class="alert alert-info">
            <h6><i class="fas fa-shield-alt me-2"></i>Segurança</h6>
            <ul class="mb-0">
                <li>Todas as chaves de API são únicas e criptografadas</li>
                <li>Contas suspensas têm suas chaves automaticamente bloqueadas</li>
                <li>Rate limiting aplicado para prevenir abuso</li>
            </ul>
        </div>

        <div class="alert alert-success">
            <h6><i class="fas fa-sync me-2"></i>Sincronização</h6>
            <ul class="mb-0">
                <li>Posts pendentes são atualizados em tempo real</li>
                <li>Status de conexão dos sites é verificado automaticamente</li>
                <li>Logs detalhados disponíveis para debugging</li>
            </ul>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../layouts/app.php';
?>
