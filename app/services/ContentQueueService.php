<?php
/**
 * Serviço de Fila de Processamento de Conteúdo
 * Sistema de Dashboard E1Copy AI
 */

class ContentQueueService {
    private $db;
    private $groqService;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->groqService = new GroqService();
    }
    
    /**
     * Adicionar item à fila
     */
    public function addToQueue($userId, $siteUrl, $topic, $options = []) {
        $data = [
            'user_id' => $userId,
            'site_id' => $options['site_id'] ?? null,
            'site_url' => $siteUrl,
            'site_name' => $options['site_name'] ?? '',
            'template_id' => $options['template_id'] ?? $this->getDefaultTemplateId(),
            'topic' => $topic,
            'keywords' => json_encode($options['keywords'] ?? []),
            'custom_variables' => json_encode($options['custom_variables'] ?? []),
            'priority' => $options['priority'] ?? 0,
            'scheduled_for' => $options['scheduled_for'] ?? null,
            'max_attempts' => $options['max_attempts'] ?? 3
        ];
        
        $queueId = $this->db->insert('content_queue', $data);
        
        if ($queueId) {
            $this->logExecution($userId, $siteUrl, 'queue_add', [
                'queue_id' => $queueId,
                'topic' => $topic
            ], 'success');
            
            return $queueId;
        }
        
        throw new Exception('Erro ao adicionar item à fila');
    }
    
    /**
     * Processar próximo item da fila
     */
    public function processNext() {
        // Buscar próximo item pendente
        $item = $this->db->fetch("
            SELECT * FROM content_queue 
            WHERE status = 'pending' 
            AND (scheduled_for IS NULL OR scheduled_for <= NOW())
            AND attempts < max_attempts
            ORDER BY priority DESC, created_at ASC 
            LIMIT 1
        ");
        
        if (!$item) {
            return null; // Nenhum item para processar
        }
        
        return $this->processItem($item['id']);
    }
    
    /**
     * Processar item específico da fila
     */
    public function processItem($queueId) {
        $startTime = microtime(true);
        
        try {
            // Marcar como processando
            $this->db->update('content_queue', [
                'status' => 'processing',
                'last_attempt_at' => date('Y-m-d H:i:s')
            ], 'id = :id', ['id' => $queueId]);
            
            // Buscar dados do item
            $item = $this->db->fetch("SELECT * FROM content_queue WHERE id = :id", ['id' => $queueId]);
            
            if (!$item) {
                throw new Exception('Item da fila não encontrado');
            }
            
            // Incrementar tentativas
            $this->db->query(
                "UPDATE content_queue SET attempts = attempts + 1 WHERE id = :id",
                ['id' => $queueId]
            );
            
            // Decodificar dados
            $keywords = json_decode($item['keywords'], true) ?: [];
            $customVariables = json_decode($item['custom_variables'], true) ?: [];
            
            // Gerar conteúdo
            $result = $this->groqService->generatePost(
                $item['template_id'],
                $item['topic'],
                $keywords,
                $customVariables
            );
            
            // Salvar post gerado
            $postData = [
                'user_id' => $item['user_id'],
                'site_id' => $item['site_id'],
                'site_url' => $item['site_url'],
                'site_name' => $item['site_name'],
                'template_id' => $item['template_id'],
                'post_title' => $result['variables']['titulo'] ?? 'Título do Post',
                'post_content' => $result['content'],
                'post_excerpt' => $result['variables']['resumo'] ?? '',
                'post_slug' => $result['variables']['slug'] ?? '',
                'post_category' => $result['variables']['categoria'] ?? '',
                'post_tags' => $result['variables']['tags'] ?? '',
                'prompt_used' => $result['prompt_used'],
                'ai_response' => $result['ai_response'],
                'variables_used' => json_encode($result['variables']),
                'generation_status' => 'completed',
                'generated_at' => date('Y-m-d H:i:s')
            ];
            
            $postId = $this->db->insert('generated_posts', $postData);
            
            // Marcar item como concluído
            $this->db->update('content_queue', [
                'status' => 'completed',
                'completed_at' => date('Y-m-d H:i:s')
            ], 'id = :id', ['id' => $queueId]);
            
            $executionTime = (microtime(true) - $startTime) * 1000;
            
            $this->logExecution($item['user_id'], $item['site_url'], 'generate_content', [
                'queue_id' => $queueId,
                'post_id' => $postId,
                'topic' => $item['topic']
            ], 'success', null, $executionTime);
            
            // Tentar publicar no WordPress se habilitado
            if ($this->isAutoPublishEnabled()) {
                $this->publishToWordPress($postId);
            }
            
            return [
                'success' => true,
                'queue_id' => $queueId,
                'post_id' => $postId,
                'execution_time' => $executionTime
            ];
            
        } catch (Exception $e) {
            $executionTime = (microtime(true) - $startTime) * 1000;
            
            // Marcar como falha
            $this->db->update('content_queue', [
                'status' => 'failed',
                'error_message' => $e->getMessage()
            ], 'id = :id', ['id' => $queueId]);
            
            $this->logExecution($item['user_id'] ?? 0, $item['site_url'] ?? '', 'generate_content', [
                'queue_id' => $queueId,
                'error' => $e->getMessage()
            ], 'error', $e->getMessage(), $executionTime);
            
            return [
                'success' => false,
                'queue_id' => $queueId,
                'error' => $e->getMessage(),
                'execution_time' => $executionTime
            ];
        }
    }
    
    /**
     * Processar toda a fila
     */
    public function processAll($maxItems = 10) {
        $processed = 0;
        $results = [];
        
        while ($processed < $maxItems) {
            $result = $this->processNext();
            
            if (!$result) {
                break; // Nenhum item para processar
            }
            
            $results[] = $result;
            $processed++;
            
            // Pequena pausa entre processamentos
            usleep(100000); // 0.1 segundo
        }
        
        return [
            'processed' => $processed,
            'results' => $results
        ];
    }
    
    /**
     * Publicar post no WordPress
     */
    public function publishToWordPress($postId) {
        $post = $this->db->fetch("SELECT * FROM generated_posts WHERE id = :id", ['id' => $postId]);
        
        if (!$post) {
            throw new Exception('Post não encontrado');
        }
        
        try {
            // Marcar como processando publicação
            $this->db->update('generated_posts', [
                'publish_status' => 'processing'
            ], 'id = :id', ['id' => $postId]);
            
            // Buscar dados de autenticação do site
            $siteData = $this->getSiteAuthData($post['site_url']);
            
            // Preparar dados do post para WordPress
            $wpPostData = [
                'title' => $post['post_title'],
                'content' => $post['post_content'],
                'excerpt' => $post['post_excerpt'],
                'slug' => $post['post_slug'],
                'status' => 'publish',
                'categories' => [$post['post_category']],
                'tags' => explode(',', $post['post_tags'])
            ];
            
            // Enviar para WordPress via REST API
            $wpResponse = $this->sendToWordPress($post['site_url'], $wpPostData, $siteData);
            
            // Atualizar status
            $this->db->update('generated_posts', [
                'publish_status' => 'published',
                'wp_post_id' => $wpResponse['id'] ?? null,
                'wp_response' => json_encode($wpResponse),
                'published_at' => date('Y-m-d H:i:s')
            ], 'id = :id', ['id' => $postId]);
            
            $this->logExecution($post['user_id'], $post['site_url'], 'publish_post', [
                'post_id' => $postId,
                'wp_post_id' => $wpResponse['id'] ?? null
            ], 'success');
            
            return $wpResponse;
            
        } catch (Exception $e) {
            // Marcar como falha na publicação
            $this->db->update('generated_posts', [
                'publish_status' => 'failed',
                'error_message' => $e->getMessage()
            ], 'id = :id', ['id' => $postId]);
            
            $this->logExecution($post['user_id'], $post['site_url'], 'publish_post', [
                'post_id' => $postId,
                'error' => $e->getMessage()
            ], 'error', $e->getMessage());
            
            throw $e;
        }
    }
    
    /**
     * Obter estatísticas da fila
     */
    public function getQueueStats() {
        return $this->db->fetch("
            SELECT 
                COUNT(*) as total_items,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
                COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled
            FROM content_queue
        ");
    }
    
    /**
     * Limpar itens antigos da fila
     */
    public function cleanupOldItems($daysOld = 30) {
        $deleted = $this->db->query(
            "DELETE FROM content_queue WHERE status IN ('completed', 'failed', 'cancelled') AND created_at < DATE_SUB(NOW(), INTERVAL :days DAY)",
            ['days' => $daysOld]
        );
        
        return $deleted;
    }
    
    /**
     * Cancelar item da fila
     */
    public function cancelItem($queueId) {
        return $this->db->update('content_queue', [
            'status' => 'cancelled'
        ], 'id = :id', ['id' => $queueId]);
    }
    
    /**
     * Reprocessar item falhado
     */
    public function retryItem($queueId) {
        return $this->db->update('content_queue', [
            'status' => 'pending',
            'attempts' => 0,
            'error_message' => null
        ], 'id = :id', ['id' => $queueId]);
    }
    
    // Métodos auxiliares privados
    
    private function getDefaultTemplateId() {
        $setting = $this->db->fetch("SELECT setting_value FROM ai_settings WHERE setting_key = 'default_template_id'");
        return $setting ? (int) $setting['setting_value'] : 1;
    }
    
    private function isAutoPublishEnabled() {
        $setting = $this->db->fetch("SELECT setting_value FROM ai_settings WHERE setting_key = 'auto_publish_enabled'");
        return $setting && $setting['setting_value'] === 'true';
    }
    
    private function getSiteAuthData($siteUrl) {
        // Implementar busca de dados de autenticação do site
        // Por enquanto retorna dados vazios
        return [];
    }
    
    private function sendToWordPress($siteUrl, $postData, $authData) {
        // Implementar envio para WordPress REST API
        // Por enquanto simula sucesso
        return ['id' => rand(1000, 9999), 'status' => 'published'];
    }
    
    private function logExecution($userId, $siteUrl, $actionType, $requestData, $status, $errorMessage = null, $executionTime = null) {
        $this->db->insert('ai_execution_logs', [
            'user_id' => $userId,
            'site_url' => $siteUrl,
            'action_type' => $actionType,
            'request_data' => json_encode($requestData),
            'execution_time_ms' => $executionTime,
            'status' => $status,
            'error_message' => $errorMessage
        ]);
    }
}
