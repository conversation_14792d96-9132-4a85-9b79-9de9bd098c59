<?php
/**
 * <PERSON><PERSON>t para criar a tabela client_posts no banco remoto
 */

// Configurações do banco remoto
$host = 'srv1845.hstgr.io';
$dbname = 'u880879026_appDash';
$username = 'u880879026_userDash';
$password = ':sX=zys@2';

try {
    // Conectar ao banco
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Conectado ao banco de dados com sucesso!\n";
    
    // SQL para criar a tabela
    $sql = "CREATE TABLE IF NOT EXISTS client_posts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        site_id INT NOT NULL,
        user_id INT NOT NULL,
        post_type ENUM('article', 'product_review') NOT NULL,
        title VARCHAR(500) NOT NULL,
        content LONGTEXT,
        excerpt TEXT,
        keywords TEXT,
        category VARCHAR(255),
        tags TEXT,
        slug VARCHAR(255),
        
        -- Campos específicos para produto review
        product_name VARCHAR(255) NULL,
        product_url TEXT NULL,
        product_price DECIMAL(10,2) NULL,
        product_rating DECIMAL(2,1) NULL,
        product_pros TEXT NULL,
        product_cons TEXT NULL,
        affiliate_link TEXT NULL,
        
        -- Status e controle
        status ENUM('draft', 'pending', 'processing', 'completed', 'failed', 'published') DEFAULT 'draft',
        wordpress_post_id INT NULL,
        error_message TEXT NULL,
        
        -- Timestamps
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        published_at TIMESTAMP NULL,
        
        -- Foreign keys
        FOREIGN KEY (site_id) REFERENCES client_sites(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        
        -- Indexes
        INDEX idx_site_status (site_id, status),
        INDEX idx_user_status (user_id, status),
        INDEX idx_post_type (post_type),
        INDEX idx_created_at (created_at)
    )";
    
    // Executar o SQL
    $pdo->exec($sql);
    echo "Tabela 'client_posts' criada com sucesso!\n";
    
    // Verificar se a tabela foi criada
    $stmt = $pdo->query("SHOW TABLES LIKE 'client_posts'");
    if ($stmt->rowCount() > 0) {
        echo "Confirmado: Tabela 'client_posts' existe no banco de dados.\n";
        
        // Mostrar estrutura da tabela
        $stmt = $pdo->query("DESCRIBE client_posts");
        echo "\nEstrutura da tabela:\n";
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
        echo "| Field           | Type             | Null | Key | Default | Extra          |\n";
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            printf("| %-15s | %-16s | %-4s | %-3s | %-7s | %-14s |\n",
                $row['Field'],
                $row['Type'],
                $row['Null'],
                $row['Key'],
                $row['Default'] ?: 'NULL',
                $row['Extra']
            );
        }
        echo "+-----------------+------------------+------+-----+---------+----------------+\n";
    } else {
        echo "ERRO: Tabela não foi criada!\n";
    }
    
} catch (PDOException $e) {
    echo "Erro: " . $e->getMessage() . "\n";
}
?>
