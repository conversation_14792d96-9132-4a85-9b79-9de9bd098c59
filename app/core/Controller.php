<?php
/**
 * Classe Base para Controllers
 * Sistema de Dashboard E1Copy AI
 */

class Controller {
    protected $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    protected function view($template, $data = []) {
        $view = new View();
        return $view->render($template, $data);
    }
    
    protected function json($data, $status = 200) {
        http_response_code($status);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    protected function redirect($url) {
        header('Location: ' . $url);
        exit;
    }
    
    protected function input($key, $default = null) {
        // Combinar dados de GET, POST e JSON
        $input = array_merge($_GET, $_POST);

        // Processar dados JSON do corpo da requisição
        if (empty($input) || !isset($input[$key])) {
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            if (strpos($contentType, 'application/json') !== false) {
                $jsonInput = json_decode(file_get_contents('php://input'), true);
                if (is_array($jsonInput)) {
                    $input = array_merge($input, $jsonInput);
                }
            }
        }

        return $input[$key] ?? $default;
    }
    
    protected function validate($rules, $data = null) {
        if ($data === null) {
            $data = array_merge($_GET, $_POST);
        }
        
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = explode('|', $rule);
            
            foreach ($ruleList as $singleRule) {
                if ($singleRule === 'required' && empty($value)) {
                    $errors[$field][] = "O campo {$field} é obrigatório";
                } elseif (strpos($singleRule, 'min:') === 0) {
                    $min = (int) substr($singleRule, 4);
                    if (strlen($value) < $min) {
                        $errors[$field][] = "O campo {$field} deve ter pelo menos {$min} caracteres";
                    }
                } elseif (strpos($singleRule, 'max:') === 0) {
                    $max = (int) substr($singleRule, 4);
                    if (strlen($value) > $max) {
                        $errors[$field][] = "O campo {$field} deve ter no máximo {$max} caracteres";
                    }
                } elseif ($singleRule === 'email' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[$field][] = "O campo {$field} deve ser um email válido";
                }
            }
        }
        
        return empty($errors) ? true : $errors;
    }
    
    protected function generateCsrfToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    protected function verifyCsrfToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    protected function flash($key, $message = null) {
        if ($message === null) {
            $value = $_SESSION['flash'][$key] ?? null;
            unset($_SESSION['flash'][$key]);
            return $value;
        }
        
        $_SESSION['flash'][$key] = $message;
    }
    
    protected function old($key, $default = null) {
        return $_SESSION['old'][$key] ?? $default;
    }
    
    protected function withInput() {
        $_SESSION['old'] = array_merge($_GET, $_POST);
        return $this;
    }
    
    protected function withErrors($errors) {
        $_SESSION['errors'] = $errors;
        return $this;
    }
}
