<?php
/**
 * Sistema de Roteamento
 * Sistema de Dashboard E1Copy AI
 */

class Router {
    private $routes = [];
    private $middlewares = [];
    
    public function get($path, $handler, $middlewares = []) {
        $this->addRoute('GET', $path, $handler, $middlewares);
    }
    
    public function post($path, $handler, $middlewares = []) {
        $this->addRoute('POST', $path, $handler, $middlewares);
    }
    
    public function put($path, $handler, $middlewares = []) {
        $this->addRoute('PUT', $path, $handler, $middlewares);
    }
    
    public function delete($path, $handler, $middlewares = []) {
        $this->addRoute('DELETE', $path, $handler, $middlewares);
    }
    
    private function addRoute($method, $path, $handler, $middlewares = []) {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler,
            'middlewares' => $middlewares
        ];
    }
    
    public function dispatch() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Remover pasta base se necessário
        $basePath = dirname($_SERVER['SCRIPT_NAME']);
        if ($basePath !== '/') {
            $path = substr($path, strlen($basePath));
        }
        
        foreach ($this->routes as $route) {
            if ($route['method'] === $method && $this->matchPath($route['path'], $path)) {
                // Executar middlewares
                foreach ($route['middlewares'] as $middleware) {
                    if (!$this->executeMiddleware($middleware)) {
                        return;
                    }
                }
                
                // Executar handler
                $this->executeHandler($route['handler'], $path, $route['path']);
                return;
            }
        }
        
        // Rota não encontrada
        http_response_code(404);
        echo "Página não encontrada";
    }
    
    private function matchPath($routePath, $requestPath) {
        // Converter parâmetros da rota para regex
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $requestPath);
    }
    
    private function executeHandler($handler, $requestPath, $routePath) {
        // Extrair parâmetros da URL
        $params = $this->extractParams($routePath, $requestPath);
        
        if (is_string($handler)) {
            // Handler é uma string no formato "Controller@method"
            list($controller, $method) = explode('@', $handler);

            // Verificar se já termina com "Controller"
            if (substr($controller, -10) === 'Controller') {
                $controllerClass = $controller;
            } else {
                $controllerClass = $controller . 'Controller';
            }
            
            if (class_exists($controllerClass)) {
                $instance = new $controllerClass();
                if (method_exists($instance, $method)) {
                    call_user_func_array([$instance, $method], $params);
                } else {
                    throw new Exception("Método {$method} não encontrado no controller {$controllerClass}");
                }
            } else {
                throw new Exception("Controller {$controllerClass} não encontrado");
            }
        } elseif (is_callable($handler)) {
            // Handler é uma função
            call_user_func_array($handler, $params);
        }
    }
    
    private function extractParams($routePath, $requestPath) {
        $routeParts = explode('/', trim($routePath, '/'));
        $requestParts = explode('/', trim($requestPath, '/'));
        
        $params = [];
        for ($i = 0; $i < count($routeParts); $i++) {
            if (preg_match('/\{([^}]+)\}/', $routeParts[$i], $matches)) {
                $params[$matches[1]] = $requestParts[$i] ?? null;
            }
        }
        
        return array_values($params);
    }
    
    private function executeMiddleware($middleware) {
        if (is_string($middleware) && class_exists($middleware)) {
            $instance = new $middleware();
            return $instance->handle();
        } elseif (is_callable($middleware)) {
            return $middleware();
        }
        
        return true;
    }
}
