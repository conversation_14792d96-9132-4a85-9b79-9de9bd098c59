<?php
/**
 * Sistema de Autenticação
 * Sistema de Dashboard E1Copy AI
 */

class Auth {
    private static $user = null;
    
    public static function attempt($email, $password) {
        $db = Database::getInstance();
        
        // Verificar tentativas de login
        if (!self::checkLoginAttempts($email)) {
            return false;
        }
        
        // Buscar usuário
        $user = $db->fetch(
            "SELECT * FROM users WHERE email = :email AND status = 'active'",
            ['email' => $email]
        );
        
        if (!$user) {
            self::logLoginAttempt($email, false);
            return false;
        }
        
        // Verificar senha
        if (!password_verify($password, $user['password'])) {
            self::logLoginAttempt($email, false);
            return false;
        }
        
        // Login bem-sucedido
        self::logLoginAttempt($email, true);
        self::login($user);
        
        return true;
    }
    
    public static function login($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_role'] = $user['role'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['login_time'] = time();
        
        // Regenerar ID da sessão por segurança
        session_regenerate_id(true);
        
        self::$user = $user;
    }
    
    public static function logout() {
        $_SESSION = [];
        session_destroy();
        self::$user = null;
    }
    
    public static function check() {
        if (self::$user !== null) {
            return true;
        }
        
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // Verificar se a sessão não expirou
        $sessionLifetime = config('app.session.lifetime', 7200);
        if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > $sessionLifetime) {
            self::logout();
            return false;
        }
        
        // Carregar dados do usuário
        $db = Database::getInstance();
        $user = $db->fetch(
            "SELECT * FROM users WHERE id = :id AND status = 'active'",
            ['id' => $_SESSION['user_id']]
        );
        
        if (!$user) {
            self::logout();
            return false;
        }
        
        self::$user = $user;
        return true;
    }
    
    public static function user() {
        if (!self::check()) {
            return null;
        }
        
        return self::$user;
    }
    
    public static function id() {
        $user = self::user();
        return $user ? $user['id'] : null;
    }
    
    public static function isAdmin() {
        $user = self::user();
        return $user && $user['role'] === 'admin';
    }
    
    public static function isClient() {
        $user = self::user();
        return $user && $user['role'] === 'client';
    }
    
    private static function checkLoginAttempts($email) {
        $db = Database::getInstance();
        $maxAttempts = config('app.security.max_login_attempts', 5);
        $lockoutDuration = config('app.security.lockout_duration', 900); // 15 minutos
        
        // Contar tentativas falhadas nas últimas X minutos
        $attempts = $db->fetch(
            "SELECT COUNT(*) as count FROM login_attempts 
             WHERE email = :email 
             AND success = 0 
             AND attempted_at > DATE_SUB(NOW(), INTERVAL :duration SECOND)",
            [
                'email' => $email,
                'duration' => $lockoutDuration
            ]
        );
        
        return $attempts['count'] < $maxAttempts;
    }
    
    private static function logLoginAttempt($email, $success) {
        $db = Database::getInstance();
        
        $db->insert('login_attempts', [
            'email' => $email,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'success' => $success ? 1 : 0
        ]);
        
        // Limpar tentativas antigas (mais de 24 horas)
        $db->query(
            "DELETE FROM login_attempts WHERE attempted_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)"
        );
    }
    
    public static function generateApiKey($userId, $subscriptionId, $name = 'Chave Principal') {
        $db = Database::getInstance();
        
        // Gerar chave única
        do {
            $apiKey = 'e1copy_' . bin2hex(random_bytes(32));
            $exists = $db->fetch("SELECT id FROM api_keys WHERE api_key = :key", ['key' => $apiKey]);
        } while ($exists);
        
        // Buscar limites do plano
        $subscription = $db->fetch(
            "SELECT s.*, p.limits_per_month 
             FROM subscriptions s 
             JOIN plans p ON s.plan_id = p.id 
             WHERE s.id = :id",
            ['id' => $subscriptionId]
        );
        
        $result = $db->insert('api_keys', [
            'user_id' => $userId,
            'subscription_id' => $subscriptionId,
            'api_key' => $apiKey,
            'name' => $name,
            'status' => 'active',
            'monthly_limit' => $subscription['limits_per_month']
        ]);
        
        if ($result) {
            return $apiKey;
        }
        
        return false;
    }
    
    public static function verifyApiKey($apiKey) {
        $db = Database::getInstance();
        
        $keyData = $db->fetch(
            "SELECT ak.*, u.status as user_status, s.status as subscription_status, s.ends_at
             FROM api_keys ak
             JOIN users u ON ak.user_id = u.id
             JOIN subscriptions s ON ak.subscription_id = s.id
             WHERE ak.api_key = :key",
            ['key' => $apiKey]
        );
        
        if (!$keyData) {
            return ['valid' => false, 'reason' => 'Chave não encontrada'];
        }
        
        // Verificar status do usuário
        if ($keyData['user_status'] !== 'active') {
            return ['valid' => false, 'reason' => 'Usuário inativo'];
        }
        
        // Verificar status da chave
        if ($keyData['status'] !== 'active') {
            return ['valid' => false, 'reason' => 'Chave suspensa ou revogada'];
        }
        
        // Verificar status da assinatura
        if ($keyData['subscription_status'] !== 'active') {
            return ['valid' => false, 'reason' => 'Assinatura inativa'];
        }
        
        // Verificar expiração da assinatura
        if ($keyData['ends_at'] && strtotime($keyData['ends_at']) < time()) {
            return ['valid' => false, 'reason' => 'Assinatura expirada'];
        }
        
        // Verificar limite mensal
        if ($keyData['monthly_limit'] && $keyData['monthly_usage'] >= $keyData['monthly_limit']) {
            return ['valid' => false, 'reason' => 'Limite mensal excedido'];
        }
        
        return [
            'valid' => true,
            'user_id' => $keyData['user_id'],
            'key_id' => $keyData['id'],
            'monthly_usage' => $keyData['monthly_usage'],
            'monthly_limit' => $keyData['monthly_limit']
        ];
    }
    
    public static function logApiUsage($keyId, $userId, $endpoint, $method, $responseStatus, $responseTime) {
        $db = Database::getInstance();
        
        // Registrar uso
        $db->insert('api_usage', [
            'api_key_id' => $keyId,
            'user_id' => $userId,
            'endpoint' => $endpoint,
            'method' => $method,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'response_status' => $responseStatus,
            'response_time_ms' => $responseTime
        ]);
        
        // Atualizar contadores na chave
        $db->query(
            "UPDATE api_keys SET 
             usage_count = usage_count + 1,
             monthly_usage = monthly_usage + 1,
             last_used_at = NOW()
             WHERE id = :id",
            ['id' => $keyId]
        );
    }
}
