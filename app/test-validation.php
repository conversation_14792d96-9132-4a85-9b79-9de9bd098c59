<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Validação - E1Copy AI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-key me-2"></i>Teste de Validação de Chave</h3>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="apiKey" class="form-label">Chave de API:</label>
                            <input type="text" class="form-control" id="apiKey" placeholder="Insira sua chave de API">
                        </div>
                        
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary" onclick="testValidation()">
                                <i class="fas fa-check me-1"></i>
                                Testar Validação
                            </button>
                            <button type="button" class="btn btn-secondary ms-2" onclick="testCors()">
                                <i class="fas fa-globe me-1"></i>
                                Testar CORS
                            </button>
                        </div>
                        
                        <div id="result" class="mt-3"></div>
                        
                        <hr>
                        
                        <h5>Informações de Teste:</h5>
                        <ul>
                            <li><strong>Endpoint de Validação:</strong> <code><?= url('/api/v1/validate') ?></code></li>
                            <li><strong>Endpoint de Registro:</strong> <code><?= url('/api/v1/register-site') ?></code></li>
                            <li><strong>Método:</strong> POST</li>
                            <li><strong>Content-Type:</strong> application/json</li>
                        </ul>
                        
                        <div class="alert alert-info">
                            <strong>Como testar:</strong>
                            <ol>
                                <li>Obtenha uma chave de API válida no <a href="<?= url('/login') ?>" target="_blank">painel do cliente</a></li>
                                <li>Insira a chave no campo acima</li>
                                <li>Clique em "Testar Validação"</li>
                                <li>Verifique se a resposta indica sucesso</li>
                            </ol>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Logs de Teste</h5>
                    </div>
                    <div class="card-body">
                        <pre id="logs" class="bg-light p-3" style="max-height: 300px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.textContent += `[${timestamp}] ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function showResult(success, message, data = null) {
            const result = document.getElementById('result');
            const alertClass = success ? 'alert-success' : 'alert-danger';
            const icon = success ? 'fas fa-check-circle' : 'fas fa-times-circle';
            
            let html = `
                <div class="alert ${alertClass}">
                    <i class="${icon} me-2"></i>
                    <strong>${success ? 'Sucesso' : 'Erro'}:</strong> ${message}
                </div>
            `;
            
            if (data) {
                html += `
                    <div class="card">
                        <div class="card-header">Dados da Resposta:</div>
                        <div class="card-body">
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    </div>
                `;
            }
            
            result.innerHTML = html;
        }
        
        function testValidation() {
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!apiKey) {
                showResult(false, 'Por favor, insira uma chave de API');
                return;
            }
            
            log('Iniciando teste de validação...');
            log(`Chave: ${apiKey.substring(0, 8)}...`);
            
            const data = {
                api_key: apiKey
            };
            
            log(`Enviando requisição para: <?= url('/api/v1/validate') ?>`);
            log(`Dados: ${JSON.stringify(data)}`);
            
            fetch('<?= url('/api/v1/validate') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                log(`Status da resposta: ${response.status}`);
                log(`Headers: ${JSON.stringify([...response.headers.entries()])}`);
                return response.json();
            })
            .then(data => {
                log(`Resposta recebida: ${JSON.stringify(data)}`);
                
                if (data.success) {
                    showResult(true, 'Chave validada com sucesso!', data);
                    
                    // Testar registro do site automaticamente
                    if (data.data && data.data.valid) {
                        setTimeout(() => testSiteRegistration(apiKey), 1000);
                    }
                } else {
                    showResult(false, data.message || 'Erro na validação', data);
                }
            })
            .catch(error => {
                log(`Erro: ${error.message}`);
                showResult(false, `Erro de conexão: ${error.message}`);
            });
        }
        
        function testSiteRegistration(apiKey) {
            log('Testando registro do site...');
            
            const data = {
                api_key: apiKey,
                site_url: window.location.origin,
                plugin_version: '1.0.0'
            };
            
            log(`Enviando requisição para: <?= url('/api/v1/register-site') ?>`);
            
            fetch('<?= url('/api/v1/register-site') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                log(`Registro do site: ${JSON.stringify(data)}`);
                
                if (data.success) {
                    log('Site registrado com sucesso!');
                } else {
                    log(`Erro no registro: ${data.message}`);
                }
            })
            .catch(error => {
                log(`Erro no registro: ${error.message}`);
            });
        }
        
        function testCors() {
            log('Testando CORS...');
            
            // Fazer uma requisição OPTIONS para testar CORS
            fetch('<?= url('/api/v1/validate') ?>', {
                method: 'OPTIONS',
                headers: {
                    'Content-Type': 'application/json',
                    'Access-Control-Request-Method': 'POST',
                    'Access-Control-Request-Headers': 'Content-Type'
                }
            })
            .then(response => {
                log(`CORS OPTIONS Status: ${response.status}`);
                log(`CORS Headers: ${JSON.stringify([...response.headers.entries()])}`);
                
                if (response.status === 200) {
                    showResult(true, 'CORS configurado corretamente');
                } else {
                    showResult(false, `CORS com problema: Status ${response.status}`);
                }
            })
            .catch(error => {
                log(`Erro CORS: ${error.message}`);
                showResult(false, `Erro CORS: ${error.message}`);
            });
        }
        
        // Log inicial
        log('Página de teste carregada');
        log(`URL atual: ${window.location.href}`);
        log(`Origin: ${window.location.origin}`);
    </script>
</body>
</html>
