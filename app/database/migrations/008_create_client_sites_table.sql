-- Tabela de sites dos clientes
CREATE TABLE IF NOT EXISTS client_sites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    site_name VARCHAR(255) NOT NULL,
    site_url VARCHAR(500) NOT NULL,
    status ENUM('pending', 'connected', 'disconnected', 'suspended') DEFAULT 'pending',
    plugin_version VARCHAR(50) NULL,
    last_connection TIMESTAMP NULL,
    api_key_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_user_url (user_id, site_url),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);
