<?php
/**
 * Script de Configuração Inicial
 * Sistema de Dashboard E1Copy AI
 */

echo "=== E1Copy AI Dashboard - Configuração Inicial ===\n\n";

// Carregar bootstrap
require_once __DIR__ . '/bootstrap.php';

try {
    echo "1. Testando conexão com o banco de dados...\n";
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    echo "✓ Conexão com banco de dados estabelecida!\n\n";
    
    echo "2. Executando migrações...\n";
    
    // Listar arquivos de migração
    $migrationFiles = glob(__DIR__ . '/database/migrations/*.sql');
    sort($migrationFiles);
    
    foreach ($migrationFiles as $file) {
        $filename = basename($file);
        echo "   Executando: {$filename}...\n";
        
        $sql = file_get_contents($file);
        $pdo->exec($sql);
        
        echo "   ✓ {$filename} executada!\n";
    }
    
    echo "\n3. Inserindo dados iniciais...\n";
    
    // Verificar se já existe usuário admin
    $adminExists = $db->fetch("SELECT id FROM users WHERE email = '<EMAIL>'");
    if (!$adminExists) {
        $adminPassword = password_hash('admin123', PASSWORD_ARGON2ID);
        $db->insert('users', [
            'name' => 'Administrador',
            'email' => '<EMAIL>',
            'password' => $adminPassword,
            'role' => 'admin',
            'status' => 'active',
            'email_verified_at' => date('Y-m-d H:i:s')
        ]);
        echo "   ✓ Usuário administrador criado!\n";
    } else {
        echo "   - Usuário administrador já existe\n";
    }
    
    // Verificar se já existem planos
    $plansExist = $db->fetch("SELECT id FROM plans LIMIT 1");
    if (!$plansExist) {
        $plans = [
            [
                'name' => 'Básico',
                'description' => 'Plano básico para pequenos sites',
                'price' => 29.90,
                'billing_cycle' => 'monthly',
                'features' => json_encode([
                    'Geração de conteúdo com IA',
                    'Até 100 posts por mês',
                    '1 site permitido',
                    'Suporte por email'
                ]),
                'limits_per_month' => 100,
                'max_sites' => 1,
                'status' => 'active'
            ],
            [
                'name' => 'Profissional',
                'description' => 'Plano para profissionais e agências',
                'price' => 79.90,
                'billing_cycle' => 'monthly',
                'features' => json_encode([
                    'Geração de conteúdo com IA',
                    'Até 500 posts por mês',
                    '5 sites permitidos',
                    'Suporte prioritário',
                    'API avançada'
                ]),
                'limits_per_month' => 500,
                'max_sites' => 5,
                'status' => 'active'
            ],
            [
                'name' => 'Empresarial',
                'description' => 'Plano para grandes empresas',
                'price' => 199.90,
                'billing_cycle' => 'monthly',
                'features' => json_encode([
                    'Geração de conteúdo com IA',
                    'Posts ilimitados',
                    'Sites ilimitados',
                    'Suporte 24/7',
                    'API completa',
                    'Relatórios avançados'
                ]),
                'limits_per_month' => null,
                'max_sites' => null,
                'status' => 'active'
            ]
        ];
        
        foreach ($plans as $plan) {
            $db->insert('plans', $plan);
        }
        echo "   ✓ Planos padrão criados!\n";
    } else {
        echo "   - Planos já existem\n";
    }
    
    // Verificar se já existem configurações
    $settingsExist = $db->fetch("SELECT id FROM settings LIMIT 1");
    if (!$settingsExist) {
        $settings = [
            [
                'key_name' => 'app_name',
                'value' => 'E1Copy AI Dashboard',
                'description' => 'Nome da aplicação',
                'type' => 'string',
                'is_public' => true
            ],
            [
                'key_name' => 'groq_api_key',
                'value' => '',
                'description' => 'Chave da API Groq para geração de conteúdo',
                'type' => 'string',
                'is_public' => false
            ],
            [
                'key_name' => 'apiflash_key',
                'value' => '',
                'description' => 'Chave da API ApiFlash para screenshots',
                'type' => 'string',
                'is_public' => false
            ],
            [
                'key_name' => 'default_prompt_instruction',
                'value' => 'escreva um artigo de três mil palavras otimizado para SEO',
                'description' => 'Instrução padrão para prompts de IA',
                'type' => 'string',
                'is_public' => false
            ],
            [
                'key_name' => 'max_api_requests_per_hour',
                'value' => '1000',
                'description' => 'Limite máximo de requisições por hora por chave',
                'type' => 'integer',
                'is_public' => false
            ],
            [
                'key_name' => 'enable_registration',
                'value' => 'false',
                'description' => 'Permitir registro de novos usuários',
                'type' => 'boolean',
                'is_public' => false
            ]
        ];
        
        foreach ($settings as $setting) {
            $db->insert('settings', $setting);
        }
        echo "   ✓ Configurações padrão criadas!\n";
    } else {
        echo "   - Configurações já existem\n";
    }
    
    echo "\n🎉 Configuração concluída com sucesso!\n\n";
    echo "=== INFORMAÇÕES IMPORTANTES ===\n";
    echo "URL do Sistema: " . config('app.url') . "\n";
    echo "Login Admin: https://app.melhorcupom.shop/admin/login\n";
    echo "Login Cliente: https://app.melhorcupom.shop/login\n\n";
    echo "Credenciais do Administrador:\n";
    echo "Email: <EMAIL>\n";
    echo "Senha: admin123\n\n";
    echo "⚠️  IMPORTANTE: Altere a senha padrão após o primeiro login!\n\n";
    echo "=== PRÓXIMOS PASSOS ===\n";
    echo "1. Acesse o painel admin e configure as chaves da API (Groq, ApiFlash)\n";
    echo "2. Crie os primeiros clientes e assinaturas\n";
    echo "3. Teste a geração de chaves de API\n";
    echo "4. Configure o plugin WordPress para usar a nova API\n\n";
    
} catch (Exception $e) {
    echo "❌ Erro durante a configuração: " . $e->getMessage() . "\n";
    echo "\nDetalhes do erro:\n";
    echo $e->getTraceAsString() . "\n";
    exit(1);
}
