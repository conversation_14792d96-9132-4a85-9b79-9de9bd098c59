<?php
/**
 * <PERSON>otas da API
 * Sistema de Dashboard E1Copy AI
 */

// Endpoints de verificação e controle
$router->get('/api/v1/verify-key', 'Api@verifyKey');
$router->post('/api/v1/verify-key', 'Api@verifyKey');
$router->post('/api/v1/validate', 'Api@validateKey');
$router->get('/api/v1/client-status/{key}', 'Api@clientStatus');
$router->post('/api/v1/suspend-key', 'Api@suspendKey');
$router->post('/api/v1/activate-key', 'Api@activateKey');
$router->post('/api/v1/status', 'Api@keyStatus');
$router->get('/api/v1/usage-stats/{key}', 'Api@usageStats');

// Endpoints para o plugin WordPress
$router->post('/api/v1/generate-content', 'Api@generateContent');
$router->post('/api/v1/screenshot', 'Api@takeScreenshot');
$router->get('/api/v1/health', 'Api@healthCheck');

// Endpoints auxiliares
$router->get('/api/v1/plans', 'Api@getPlans');
$router->post('/api/v1/auth/token', 'Api@generateToken');
$router->post('/api/v1/register-site', 'Api@registerSite');

// Endpoints para listar posts pendentes de todos os sites
$router->get('/api/v1/pending-posts', 'Api@getPendingPosts');
$router->get('/api/v1/pending-posts/by-client', 'Api@getPendingPostsByClient');
$router->get('/api/v1/pending-posts/by-site', 'Api@getPendingPostsBySite');

// Endpoints para posts criados pelos clientes no painel
$router->get('/api/v1/client-posts', 'Api@getClientPosts');
$router->get('/api/v1/client-posts/pending', 'Api@getPendingClientPosts');
