<?php
/**
 * Rotas Web da Aplicação
 * Sistema de Dashboard E1Copy AI
 */

// Página inicial - redirecionar para login
$router->get('/', function() {
    if (Auth::check()) {
        if (Auth::isAdmin()) {
            redirect(url('/admin/dashboard'));
        } else {
            redirect(url('/client/dashboard'));
        }
    } else {
        redirect(url('/login'));
    }
});

// Página de teste CSS
$router->get('/test-css', function() {
    include __DIR__ . '/../test-css.php';
});

// Página de teste de validação
$router->get('/test-validation', function() {
    include __DIR__ . '/../test-validation.php';
});

// Página de teste de API
$router->get('/test-api', function() {
    include __DIR__ . '/../test-api.php';
});

// Teste PHP de validação
$router->post('/test-php-validation', function() {
    include __DIR__ . '/../test-api.php';
});

// Verificação de chave específica
$router->get('/check-key', function() {
    header('Content-Type: text/plain');
    include __DIR__ . '/../check-key.php';
});

// Rotas de autenticação
$router->get('/login', 'Auth@showLogin');
$router->post('/login', 'Auth@login');
$router->get('/logout', 'Auth@logout');

// Rotas do cliente
$router->get('/client/dashboard', 'Client@dashboard', ['AuthMiddleware']);
$router->get('/client/account', 'Client@account', ['AuthMiddleware']);
$router->post('/client/account', 'Client@updateAccount', ['AuthMiddleware']);
$router->get('/client/sites', 'Client@sites', ['AuthMiddleware']);
$router->post('/client/sites/add', 'Client@addSite', ['AuthMiddleware']);
$router->post('/client/sites/remove/{id}', 'Client@removeSite', ['AuthMiddleware']);
$router->post('/client/sites/check/{id}', 'Client@checkSiteConnection', ['AuthMiddleware']);
$router->get('/client/sites/api-key/{id}', 'Client@getSiteApiKey', ['AuthMiddleware']);
$router->post('/client/sites/regenerate-api-key/{id}', 'Client@regenerateSiteApiKey', ['AuthMiddleware']);
$router->post('/client/generate-api-key', 'Client@generateApiKey', ['AuthMiddleware']);
$router->post('/client/revoke-api-key/{id}', 'Client@revokeApiKey', ['AuthMiddleware']);

// Rotas do admin
$router->get('/admin/login', 'Admin@showLogin');
$router->post('/admin/login', 'Admin@login');
$router->get('/admin/dashboard', 'Admin@dashboard', ['AdminMiddleware']);
$router->get('/admin/clients', 'Admin@clients', ['AdminMiddleware']);
$router->post('/admin/clients/create', 'Admin@createClient', ['AdminMiddleware']);
$router->get('/admin/clients/{id}', 'Admin@showClient', ['AdminMiddleware']);
$router->post('/admin/clients/{id}', 'Admin@updateClient', ['AdminMiddleware']);
$router->get('/admin/plans', 'Admin@plans', ['AdminMiddleware']);
$router->post('/admin/plans/create', 'Admin@createPlan', ['AdminMiddleware']);
$router->post('/admin/plans/{id}', 'Admin@updatePlan', ['AdminMiddleware']);
$router->get('/admin/settings', 'Admin@settings', ['AdminMiddleware']);
$router->post('/admin/settings', 'Admin@updateSettings', ['AdminMiddleware']);
$router->get('/admin/account', 'Admin@account', ['AdminMiddleware']);
$router->post('/admin/account', 'Admin@updateAccount', ['AdminMiddleware']);
$router->get('/admin/api-docs', 'Admin@apiDocs', ['AdminMiddleware']);
$router->get('/admin/key-diagnostic', function() {
    include __DIR__ . '/../admin/key-diagnostic.php';
}, ['AdminMiddleware']);

// Rotas de Relatórios
$router->get('/admin/reports/posts-clients', 'Reports@postsClients', ['AdminMiddleware']);
$router->get('/admin/reports/ai-usage', 'Reports@aiUsage', ['AdminMiddleware']);

// Rotas de Configurações de IA
$router->get('/admin/ai-settings', 'AiSettings@index', ['AdminMiddleware']);
$router->post('/admin/ai-settings', 'AiSettings@update', ['AdminMiddleware']);
$router->get('/admin/ai-settings/templates', 'AiSettings@templates', ['AdminMiddleware']);
$router->post('/admin/ai-settings/templates', 'AiSettings@saveTemplate', ['AdminMiddleware']);
$router->post('/admin/ai-settings/templates/{id}', 'AiSettings@updateTemplate', ['AdminMiddleware']);
$router->delete('/admin/ai-settings/templates/{id}', 'AiSettings@deleteTemplate', ['AdminMiddleware']);

// Rotas de Geração de Conteúdo
$router->post('/api/content/add-to-queue', 'Content@addToQueue', ['AuthMiddleware']);
$router->post('/api/content/generate-now', 'Content@generateNow', ['AuthMiddleware']);
$router->post('/api/content/publish', 'Content@publishPost', ['AuthMiddleware']);
$router->get('/api/content/posts', 'Content@listPosts', ['AuthMiddleware']);
$router->post('/api/content/process-queue', 'Content@processQueue', ['AdminMiddleware']);
$router->get('/api/content/queue-stats', 'Content@getQueueStats', ['AdminMiddleware']);
$router->post('/api/content/cancel-queue-item', 'Content@cancelQueueItem', ['AuthMiddleware']);
$router->post('/api/content/retry-queue-item', 'Content@retryQueueItem', ['AuthMiddleware']);

// Rotas de Sincronização de Sites
$router->post('/api/sites/sync-all', 'Reports@syncAllSites', ['AdminMiddleware']);
$router->post('/api/sites/sync/{id}', 'Reports@syncSite', ['AdminMiddleware']);

// Rotas de Ferramentas (Admin)
$router->get('/admin/tools', 'Tools@index', ['AdminMiddleware']);
$router->post('/api/tools/test-plugin-connection', 'Tools@testPluginConnection', ['AdminMiddleware']);
$router->post('/api/tools/validate-api-key', 'Tools@validateApiKey', ['AdminMiddleware']);
$router->post('/api/tools/debug-site', 'Tools@debugSite', ['AdminMiddleware']);
$router->post('/api/tools/test-endpoints', 'Tools@testEndpoints', ['AdminMiddleware']);
$router->post('/api/tools/check-database', 'Tools@checkDatabase', ['AdminMiddleware']);
$router->post('/api/tools/system-info', 'Tools@systemInfo', ['AdminMiddleware']);
$router->post('/api/tools/test-sync', 'Tools@testSyncronization', ['AdminMiddleware']);
$router->post('/api/tools/test-wp-posts', 'Tools@testWordPressPosts', ['AdminMiddleware']);
