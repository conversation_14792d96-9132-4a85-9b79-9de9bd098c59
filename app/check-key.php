<?php
/**
 * Verificação rápida de chave específica
 */

require_once __DIR__ . '/bootstrap.php';

$testKey = 'e1copy_58211d1987fae226a0e2e73dd00ebe939aa9f';

echo "🔍 Verificando chave: $testKey\n\n";

$db = Database::getInstance();

try {
    // 1. Verificar se a chave existe
    $keyData = $db->fetch("SELECT * FROM api_keys WHERE api_key = ?", [$testKey]);
    
    if ($keyData) {
        echo "✅ Chave encontrada!\n";
        echo "ID: {$keyData['id']}\n";
        echo "Nome: {$keyData['name']}\n";
        echo "Status: {$keyData['status']}\n";
        echo "User ID: {$keyData['user_id']}\n";
        echo "Subscription ID: {$keyData['subscription_id']}\n";
        echo "Uso mensal: {$keyData['monthly_usage']}/{$keyData['monthly_limit']}\n";
        echo "Último uso: {$keyData['last_used_at']}\n";
        echo "Criada em: {$keyData['created_at']}\n\n";
        
        // 2. Verificar usuário
        $user = $db->fetch("SELECT * FROM users WHERE id = ?", [$keyData['user_id']]);
        if ($user) {
            echo "👤 Usuário: {$user['name']} ({$user['email']})\n";
            echo "Status do usuário: {$user['status']}\n\n";
        }
        
        // 3. Verificar assinatura
        $subscription = $db->fetch("SELECT * FROM subscriptions WHERE id = ?", [$keyData['subscription_id']]);
        if ($subscription) {
            echo "📋 Assinatura:\n";
            echo "Status: {$subscription['status']}\n";
            echo "Plano ID: {$subscription['plan_id']}\n";
            echo "Início: {$subscription['starts_at']}\n";
            echo "Fim: {$subscription['ends_at']}\n\n";
        }
        
        // 4. Testar verificação
        echo "🧪 Testando verificação...\n";
        $verification = Auth::verifyApiKey($testKey);
        
        echo "Resultado: " . ($verification['valid'] ? 'VÁLIDA' : 'INVÁLIDA') . "\n";
        if (!$verification['valid']) {
            echo "Motivo: {$verification['reason']}\n";
        }
        
    } else {
        echo "❌ Chave NÃO encontrada!\n\n";
        
        // Listar chaves existentes
        echo "📋 Chaves existentes:\n";
        $keys = $db->fetchAll("SELECT api_key, name, status FROM api_keys LIMIT 5");
        foreach ($keys as $key) {
            echo "- {$key['api_key']} ({$key['name']}) - {$key['status']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
}

echo "\n🎯 Verificação concluída!\n";
?>
