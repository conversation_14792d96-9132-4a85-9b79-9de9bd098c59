<?php
/**
 * Script para testar a criação de novos afiliados
 * Execute este arquivo no WordPress para testar se novos afiliados funcionam corretamente
 */

// Verificar se estamos no WordPress
if (!defined('ABSPATH')) {
    die('Este script deve ser executado dentro do WordPress');
}

echo "<h2>Teste de Criação de Novo Afiliado</h2>";

// Dados de teste para um novo afiliado
$test_data = [
    'title' => 'Teste Afiliado - ' . date('Y-m-d H:i:s'),
    'affiliate_link' => 'https://exemplo.com/produto-teste',
    'card_text' => 'Este é um produto de teste para verificar se o afiliado está funcionando corretamente.',
    'button_text' => '', // Deixar vazio para testar o valor padrão
    'product_image' => 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w0NzEyNjZ8MHwxfHNlYXJjaHwxfHxzaG9lfGVufDB8MHx8fDE3MjEwNDEzNjd8MA&ixlib=rb-4.0.3&q=80&w=1080',
    'template_id' => 'model1'
];

echo "<h3>1. Testando geração de conteúdo</h3>";

// Testar a função de geração de template
if (function_exists('e1copy_ai_generate_template_content')) {
    $generated_content = e1copy_ai_generate_template_content($test_data);
    
    if (!empty($generated_content)) {
        echo "✅ Conteúdo gerado com sucesso<br>";
        echo "<strong>Tamanho do conteúdo:</strong> " . strlen($generated_content) . " caracteres<br>";
        
        // Verificar se contém elementos esperados
        $checks = [
            'card' => strpos($generated_content, 'class="card"') !== false,
            'title' => strpos($generated_content, $test_data['title']) !== false,
            'text' => strpos($generated_content, $test_data['card_text']) !== false,
            'button' => strpos($generated_content, 'Comprar') !== false,
            'image' => strpos($generated_content, 'card-img-top') !== false,
            'link' => strpos($generated_content, $test_data['affiliate_link']) !== false
        ];
        
        foreach ($checks as $element => $found) {
            echo ($found ? "✅" : "❌") . " $element: " . ($found ? "encontrado" : "não encontrado") . "<br>";
        }
        
        echo "<h4>Preview do conteúdo gerado:</h4>";
        echo "<div style='border: 1px solid #ddd; padding: 15px; background: #f9f9f9; max-width: 400px;'>";
        echo $generated_content;
        echo "</div>";
        
    } else {
        echo "❌ Falha ao gerar conteúdo<br>";
    }
} else {
    echo "❌ Função de geração de template não encontrada<br>";
}

echo "<h3>2. Testando salvamento de afiliado</h3>";

// Testar o salvamento do afiliado
if (function_exists('e1copy_ai_save_affiliate')) {
    $affiliate_id = e1copy_ai_save_affiliate($test_data);
    
    if ($affiliate_id && $affiliate_id > 0) {
        echo "✅ Afiliado salvo com sucesso - ID: $affiliate_id<br>";
        
        // Verificar se foi salvo corretamente
        if (function_exists('e1copy_ai_get_affiliate')) {
            $saved_affiliate = e1copy_ai_get_affiliate($affiliate_id);
            
            if ($saved_affiliate) {
                echo "✅ Afiliado recuperado com sucesso<br>";
                echo "<strong>Título:</strong> " . $saved_affiliate['title'] . "<br>";
                echo "<strong>Texto do botão:</strong> " . $saved_affiliate['button_text'] . "<br>";
                echo "<strong>Link:</strong> " . $saved_affiliate['affiliate_link'] . "<br>";
                echo "<strong>Shortcode:</strong> [e1copy_affiliate id=\"$affiliate_id\"]<br>";
                
                // Verificar se o texto do botão é "Comprar"
                if ($saved_affiliate['button_text'] === 'Comprar') {
                    echo "✅ Texto padrão do botão correto: 'Comprar'<br>";
                } else {
                    echo "❌ Texto do botão incorreto: '" . $saved_affiliate['button_text'] . "' (esperado: 'Comprar')<br>";
                }
                
                echo "<h4>Teste do shortcode:</h4>";
                if (function_exists('e1copy_ai_affiliate_shortcode')) {
                    $shortcode_output = e1copy_ai_affiliate_shortcode(['id' => $affiliate_id]);
                    
                    if (!empty($shortcode_output)) {
                        echo "✅ Shortcode funcionando<br>";
                        echo "<div style='border: 1px solid #ddd; padding: 15px; background: #f9f9f9; max-width: 400px;'>";
                        echo $shortcode_output;
                        echo "</div>";
                    } else {
                        echo "❌ Shortcode não retornou conteúdo<br>";
                    }
                } else {
                    echo "❌ Função de shortcode não encontrada<br>";
                }
                
                // Limpar o afiliado de teste
                echo "<h4>Limpeza:</h4>";
                if (function_exists('e1copy_ai_delete_affiliate')) {
                    $deleted = e1copy_ai_delete_affiliate($affiliate_id);
                    if ($deleted) {
                        echo "✅ Afiliado de teste removido<br>";
                    } else {
                        echo "⚠️ Não foi possível remover o afiliado de teste (ID: $affiliate_id)<br>";
                    }
                }
                
            } else {
                echo "❌ Não foi possível recuperar o afiliado salvo<br>";
            }
        }
    } else {
        echo "❌ Falha ao salvar afiliado<br>";
    }
} else {
    echo "❌ Função de salvamento não encontrada<br>";
}

echo "<h3>Teste Concluído</h3>";
echo "<p><strong>Resumo:</strong></p>";
echo "<ul>";
echo "<li>Se todos os testes passaram, novos afiliados devem funcionar corretamente</li>";
echo "<li>O texto padrão do botão deve ser 'Comprar' para novos afiliados</li>";
echo "<li>Afiliados existentes não são afetados</li>";
echo "<li>Posts existentes não devem mais ter problemas de conteúdo oculto</li>";
echo "</ul>";
?>
