<?php
function e1copy_ai_settings_page() {
    // Garantir que o Dashicons esteja disponível
    wp_enqueue_style('dashicons');
    // Verifica se o formulário foi submetido
    if (isset($_POST['submit'])) {
        // Verifica o nonce para segurança
        check_admin_referer('e1copy_ai_settings_update');

        $activation_key = sanitize_text_field($_POST['e1copy_ai_activation_key']);

        // Validar chave de ativação se fornecida
        if (!empty($activation_key)) {
            $validation_result = e1copy_ai_validate_activation_key($activation_key);
            if ($validation_result['valid']) {
                // Registrar site automaticamente se a chave for válida
                e1copy_ai_register_site($activation_key);
                add_settings_error(
                    'e1copy_ai_messages',
                    'e1copy_ai_message',
                    __('Chave de ativação válida! Site registrado com sucesso.', 'e1copy-ai'),
                    'success'
                );
            } else {
                add_settings_error(
                    'e1copy_ai_messages',
                    'e1copy_ai_message',
                    __('Chave de ativação inválida: ' . $validation_result['message'], 'e1copy-ai'),
                    'error'
                );
            }
        }

        // Salva todas as opções
        update_option('e1copy_ai_activation_key', $activation_key);

        // Mensagem de sucesso se não houve erro na validação
        if (empty($activation_key) || $validation_result['valid']) {
            add_settings_error(
                'e1copy_ai_messages',
                'e1copy_ai_message',
                __('Configurações salvas com sucesso!', 'e1copy-ai'),
                'success'
            );
        }
    }

    // Obtém os valores atuais
    $activation_key = get_option('e1copy_ai_activation_key');

    // Mostra erros/notificações
    settings_errors('e1copy_ai_messages');
?>
<div class="wrap">
    <h1><?php _e('Configurações do E1Copy AI', 'e1copy-ai'); ?></h1>

    <form method="post">
        <?php wp_nonce_field('e1copy_ai_settings_update'); ?>

        <h2 class="title"><?php _e('Configurações de Ativação', 'e1copy-ai'); ?></h2>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="e1copy_ai_activation_key"><?php _e('Chave de Ativação', 'e1copy-ai'); ?></label>
                </th>
                <td>
                    <input type="password" name="e1copy_ai_activation_key" id="e1copy_ai_activation_key"
                           value="<?php echo esc_attr($activation_key); ?>" class="regular-text">
                    <div class="activation-key-controls" style="margin: 10px 0;">
                        <button type="button" id="toggle-activation-key" class="button button-secondary">
                            <span class="dashicons dashicons-visibility"></span> Mostrar/Ocultar
                        </button>
                        <button type="button" id="validate-activation-key" class="button button-primary" style="margin-left: 10px;">
                            <span class="dashicons dashicons-yes"></span> Validar Chave
                        </button>
                        <span id="activation-status" style="margin-left: 10px;"></span>
                    </div>
                    <p class="description">
                        <?php _e('Chave de ativação fornecida pelo Dashboard E1Copy AI. Esta chave é necessária para liberar todas as funcionalidades do plugin.', 'e1copy-ai'); ?>
                        <br><strong><?php _e('Como obter sua chave:', 'e1copy-ai'); ?></strong>
                    </p>
                    <div class="notice notice-info inline" style="margin: 10px 0; padding: 10px;">
                        <p><strong>📋 Passo a passo:</strong></p>
                        <ol style="margin-left: 20px;">
                            <li>Acesse <a href="https://app.melhorcupom.shop/login" target="_blank"><strong>app.melhorcupom.shop</strong></a></li>
                            <li>Faça login ou crie sua conta</li>
                            <li>Vá em <strong>"Meus Sites"</strong></li>
                            <li>Clique em <strong>"Adicionar Site"</strong></li>
                            <li>Preencha os dados do seu site WordPress</li>
                            <li>A licença será gerada automaticamente</li>
                            <li>Clique no botão <strong>"🔑"</strong> para ver a licença</li>
                            <li>Copie a licença e cole aqui</li>
                            <li>Clique em <strong>"Validar Chave"</strong></li>
                        </ol>
                        <p><strong>💡 Dica:</strong> A licença deve começar com <code>e1copy_</code> seguido de 32 ou 64 caracteres hexadecimais.</p>
                        <p><strong>🎯 Importante:</strong> Cada site precisa de sua própria licença. Não compartilhe licenças entre sites diferentes.</p>
                    </div>
                </td>
            </tr>
        </table>

        <div class="notice notice-info" style="margin: 15px 0;">
            <p><strong>ℹ️ Sobre a API:</strong></p>
            <ul style="margin-left: 20px;">
                <li>A <strong>chave de ativação</strong> é usada automaticamente para autenticar a API REST</li>
                <li>O dashboard E1Copy AI usa a mesma chave de ativação para se conectar ao plugin</li>
                <li>Não é necessário configurar chaves adicionais</li>
                <li>Para documentação completa, acesse o painel administrativo do dashboard</li>
            </ul>
        </div>







        <?php submit_button(__('Salvar Configurações', 'e1copy-ai')); ?>
    </form>

<script>
var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
jQuery(document).ready(function($) {
    // Toggle mostrar/ocultar chave de ativação
    $('#toggle-activation-key').on('click', function() {
        var input = $('#e1copy_ai_activation_key');
        if (input.attr('type') === 'password') {
            input.attr('type', 'text');
        } else {
            input.attr('type', 'password');
        }
    });

    // Limpar status quando o usuário digita
    $('#e1copy_ai_activation_key').on('input', function() {
        $('#activation-status').html('');
    });

    // Validar chave de ativação
    $('#validate-activation-key').on('click', function() {
        var button = $(this);
        var statusSpan = $('#activation-status');
        var activationKey = $('#e1copy_ai_activation_key').val();

        if (!activationKey) {
            statusSpan.html('<span style="color: #d63638;"><span class="dashicons dashicons-warning"></span> Insira uma chave de ativação</span>');
            return;
        }

        button.prop('disabled', true);
        button.html('<span class="dashicons dashicons-update spin"></span> Validando...');
        statusSpan.html('<span style="color: #666;"><span class="dashicons dashicons-clock"></span> Verificando...</span>');

        // Usar AJAX interno do WordPress
        $.ajax({
            url: ajaxurl,
            method: 'POST',
            data: {
                action: 'e1copy_validate_key',
                activation_key: activationKey,
                nonce: '<?php echo wp_create_nonce('e1copy_ai_validate_key'); ?>'
            },
            timeout: 15000,
            success: function(response) {
                if (response.valid) {
                    statusSpan.html('<span style="color: #00a32a;"><span class="dashicons dashicons-yes-alt"></span> Chave válida e ativa!</span>');
                } else {
                    statusSpan.html('<span style="color: #d63638;"><span class="dashicons dashicons-dismiss"></span> ' + response.message + '</span>');
                }
            },
            error: function(xhr, status, error) {
                console.error('Erro na validação:', error);
                if (status === 'timeout') {
                    statusSpan.html('<span style="color: #d63638;"><span class="dashicons dashicons-warning"></span> Timeout - Verifique sua conexão</span>');
                } else {
                    statusSpan.html('<span style="color: #d63638;"><span class="dashicons dashicons-warning"></span> Erro na validação</span>');
                }
            },
            complete: function() {
                button.prop('disabled', false);
                button.html('<span class="dashicons dashicons-yes"></span> Validar Chave');
            }
        });
    });

    // Não mostrar nenhuma mensagem automática - apenas quando o usuário clicar em "Validar Chave"
});
</script>

<style>
.activation-key-controls .button {
    vertical-align: middle;
}

.activation-key-controls .dashicons {
    vertical-align: middle;
    margin-right: 5px;
}

#activation-status {
    font-weight: 500;
    vertical-align: middle;
}

#activation-status .dashicons {
    vertical-align: middle;
    margin-right: 3px;
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>










</div>
<?php } ?>