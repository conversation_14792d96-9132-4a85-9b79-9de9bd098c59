<?php
if (!defined('ABSPATH')) exit; // Bloqueia acesso direto

function e1copy_ai_home_page() {
    // Garantir que os Dashicons sejam carregados
    wp_enqueue_style('dashicons');

    // Verificar se o plugin está ativado (verificação em tempo real)
    $is_activated = e1copy_ai_is_activated(true);
?>
    <div class="wrap">
        <h1>Bem-vindo ao E1Copy AI</h1>

        <?php if (!$is_activated): ?>
            <div class="notice notice-error">
                <p><strong>Plugin não ativado!</strong> Para usar todas as funcionalidades do E1Copy AI, você precisa configurar sua chave de ativação.</p>
                <p><a href="<?= admin_url('admin.php?page=e1copy-ai-settings') ?>" class="button button-primary">Configurar Chave de Ativação</a></p>
            </div>
            <p>Obtenha sua chave de ativação em: <a href="https://app.melhorcupom.shop/login" target="_blank">app.melhorcupom.shop</a></p>
        <?php else: ?>
            <div class="notice notice-success">
                <p><strong>Plugin ativado!</strong> Todas as funcionalidades estão disponíveis.</p>
            </div>
            <p>Este plugin permite que você crie e gerencie posts e páginas para seu site WordPress, com integração com o n8n workflow para processamento de conteúdo.</p>
        <?php endif; ?>

        <h2>O que deseja fazer?</h2>
        <div style="display: flex; gap: 20px; flex-wrap: wrap; margin-top:20px;">

            <div style="background: #fff; border: 1px solid #ccd0d4; border-radius:8px; padding:20px; width:280px; box-shadow:0 2px 5px rgba(0,0,0,0.05); transition: transform 0.2s;">
                <div style="text-align:center; margin-bottom:15px;">
                    <!-- Heroicon: Document Plus para 'Gerar Novo Post' -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#4285F4" width="40" height="40">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M12 10.5v6m3-3h-6m9 6.75A2.25 2.25 0 0 1 16.25 21H7.75A2.25 2.25 0 0 1 5.5 18.75V5.25A2.25 2.25 0 0 1 7.75 3h4.982c.464 0 .91.184 1.237.513l3.518 3.518c.329.328.513.773.513 1.237V18.75z" />
                    </svg>
                </div>
                <h3 style="text-align:center; margin-bottom:15px;">Novo Post</h3>
                <p style="text-align:center; color:#666;">Crie um novo post para ser processado pelo n8n workflow.</p>
                <div style="text-align:center; margin-top:20px;">
                    <?php if ($is_activated): ?>
                        <a href="#" id="home-new-post-button" class="button button-primary" style="padding:8px 20px; height:auto;">Novo Post</a>
                    <?php else: ?>
                        <button class="button button-primary" disabled style="padding:8px 20px; height:auto; opacity:0.5;">Novo Post</button>
                        <p style="font-size:11px; color:#d63638; margin-top:5px;">Requer ativação</p>
                    <?php endif; ?>
                </div>
            </div>

            <div style="background: #fff; border: 1px solid #ccd0d4; border-radius:8px; padding:20px; width:280px; box-shadow:0 2px 5px rgba(0,0,0,0.05); transition: transform 0.2s;">
                <div style="text-align:center; margin-bottom:15px;">
                    <!-- Heroicon: Squares Plus para 'Clonar Página' -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#4285F4" width="40" height="40">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 6.75A2.25 2.25 0 0 1 6.75 4.5h3A2.25 2.25 0 0 1 12 6.75v3a2.25 2.25 0 0 1-2.25 2.25h-3A2.25 2.25 0 0 1 4.5 9.75v-3zM4.5 17.25A2.25 2.25 0 0 1 6.75 15h3a2.25 2.25 0 0 1 2.25 2.25v3a2.25 2.25 0 0 1-2.25 2.25h-3A2.25 2.25 0 0 1 4.5 20.25v-3zM15 6h6m-3-3v6" />
                    </svg>
                </div>
                <h3 style="text-align:center; margin-bottom:15px;">Clonar Página</h3>
                <p style="text-align:center; color:#666;">Clone rapidamente qualquer página da web para usar como base para seus projetos ou campanhas.</p>
                <div style="text-align:center; margin-top:20px;">
                    <?php if ($is_activated): ?>
                        <a href="admin.php?page=e1copy-ai-screenshot" class="button" style="padding:8px 20px; height:auto;">Clonar Página</a>
                    <?php else: ?>
                        <button class="button" disabled style="padding:8px 20px; height:auto; opacity:0.5;">Clonar Página</button>
                        <p style="font-size:11px; color:#d63638; margin-top:5px;">Requer ativação</p>
                    <?php endif; ?>
                </div>
            </div>

            <div style="background: #fff; border: 1px solid #ccd0d4; border-radius:8px; padding:20px; width:280px; box-shadow:0 2px 5px rgba(0,0,0,0.05); transition: transform 0.2s;">
                <div style="text-align:center; margin-bottom:15px;">
                    <!-- Heroicon: Collection para 'Minhas Páginas' -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#4285F4" width="40" height="40">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 5.25c0-1.243 1.007-2.25 2.25-2.25h12c1.243 0 2.25 1.007 2.25 2.25v12c0 1.243-1.007 2.25-2.25 2.25h-12A2.25 2.25 0 0 1 3.75 17.25v-12z" />
                      <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3h9m-9 3h6" />
                    </svg>
                </div>
                <h3 style="text-align:center; margin-bottom:15px;">Minhas Páginas</h3>
                <p style="text-align:center; color:#666;">Gerencie e edite as páginas clonadas pelo plugin de forma simples e rápida.</p>
                <div style="text-align:center; margin-top:20px;">
                    <?php if ($is_activated): ?>
                        <a href="admin.php?page=e1copy-ai-my-pages" class="button" style="padding:8px 20px; height:auto;">Minhas Páginas</a>
                    <?php else: ?>
                        <button class="button" disabled style="padding:8px 20px; height:auto; opacity:0.5;">Minhas Páginas</button>
                        <p style="font-size:11px; color:#d63638; margin-top:5px;">Requer ativação</p>
                    <?php endif; ?>
                </div>
            </div>

            <div style="background: #fff; border: 1px solid #ccd0d4; border-radius:8px; padding:20px; width:280px; box-shadow:0 2px 5px rgba(0,0,0,0.05); transition: transform 0.2s;">
                <div style="text-align:center; margin-bottom:15px;">
                    <!-- Heroicon: Document Text para 'Meus Posts' -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#4285F4" width="40" height="40">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                    </svg>
                </div>
                <h3 style="text-align:center; margin-bottom:15px;">Meus Posts</h3>
                <p style="text-align:center; color:#666;">Gerencie os posts criados pelo plugin e acompanhe seu processamento.</p>
                <div style="text-align:center; margin-top:20px;">
                    <?php if ($is_activated): ?>
                        <a href="admin.php?page=e1copy-ai-my-posts" class="button" style="padding:8px 20px; height:auto;">Meus Posts</a>
                    <?php else: ?>
                        <button class="button" disabled style="padding:8px 20px; height:auto; opacity:0.5;">Meus Posts</button>
                        <p style="font-size:11px; color:#d63638; margin-top:5px;">Requer ativação</p>
                    <?php endif; ?>
                </div>
            </div>

            <div style="background: #fff; border: 1px solid #ccd0d4; border-radius:8px; padding:20px; width:280px; box-shadow:0 2px 5px rgba(0,0,0,0.05); transition: transform 0.2s;">
                <div style="text-align:center; margin-bottom:15px;">
                    <!-- Heroicon: Link para 'Meus Afiliados' -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#4285F4" width="40" height="40">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
                    </svg>
                </div>
                <h3 style="text-align:center; margin-bottom:15px;">Meus Afiliados</h3>
                <p style="text-align:center; color:#666;">Gerencie seus links de afiliados e CTAs para usar em seus posts.</p>
                <div style="text-align:center; margin-top:20px;">
                    <?php if ($is_activated): ?>
                        <a href="admin.php?page=e1copy-ai-affiliates" class="button" style="padding:8px 20px; height:auto;">Meus Afiliados</a>
                    <?php else: ?>
                        <button class="button" disabled style="padding:8px 20px; height:auto; opacity:0.5;">Meus Afiliados</button>
                        <p style="font-size:11px; color:#d63638; margin-top:5px;">Requer ativação</p>
                    <?php endif; ?>
                </div>
            </div>

            <div style="background: #fff; border: 1px solid #ccd0d4; border-radius:8px; padding:20px; width:280px; box-shadow:0 2px 5px rgba(0,0,0,0.05); transition: transform 0.2s;">
                <div style="text-align:center; margin-bottom:15px;">
                    <!-- Heroicon: Cog 6 Tooth para 'Configurar API' -->
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#4285F4" width="40" height="40">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 0 1 1.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.808-.107 1.206.165.396.505.71.93.78l.893.149c.543.09.94.56.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.893.149c-.425.07-.765.384-.93.78-.165.398-.143.856.107 1.206l.527.737c.32.448.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 0 1-1.449.12l-.738-.527c-.35-.25-.807-.272-1.205-.107-.396.165-.71.505-.78.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.149-.894c-.07-.424-.384-.764-.78-.93-.398-.164-.855-.142-1.205.108l-.737.527c-.448.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 0 1-.12-1.45l.527-.737c.25-.35.273-.808.108-1.206-.165-.396-.505-.71-.93-.78l-.894-.149c-.542-.09-.94-.56-.94-1.11v-1.094c0-.55.398-1.019.94-1.11l.894-.149c.424-.07.765-.384.93-.78.165-.398.142-.856-.108-1.206l-.527-.737a1.125 1.125 0 0 1 .12-1.45l.774-.773a1.125 1.125 0 0 1 1.45-.12l.737.527c.35.25.808.272 1.205.107.397-.165.71-.505.78-.929l.149-.894z" />
                      <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0z" />
                    </svg>
                </div>
                <h3 style="text-align:center; margin-bottom:15px;">Configurações</h3>
                <p style="text-align:center; color:#666;">Configure as chaves de API, instruções personalizadas e outras opções do plugin.</p>
                <div style="text-align:center; margin-top:20px;">
                    <a href="admin.php?page=e1copy-ai-settings" class="button" style="padding:8px 20px; height:auto;">Configurar</a>
                </div>
            </div>

        </div>

        <!-- Modal de seleção de tipo de post -->
        <div id="post-type-modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.4);">
            <div style="background-color: #fefefe; margin: 15% auto; padding: 20px; border: 1px solid #888; width: 50%; max-width: 500px; border-radius: 5px; box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);">
                <h2 style="margin-top: 0;">Selecione o tipo de post</h2>
                <p>Escolha o tipo de conteúdo que deseja criar:</p>
                <div style="display: flex; justify-content: space-between; margin-top: 20px;">
                    <a href="<?php echo admin_url('admin.php?page=e1copy-ai-my-posts&action=new&type=blog'); ?>" class="button button-primary" style="width: 48%; text-align: center; padding: 15px 0; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                        <span class="dashicons dashicons-welcome-write-blog" style="font-size: 24px; width: 24px; height: 24px; margin-bottom: 8px;"></span>
                        Blog Tradicional
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=e1copy-ai-my-posts&action=new&type=product'); ?>" class="button button-primary" style="width: 48%; text-align: center; padding: 15px 0; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                        <span class="dashicons dashicons-cart" style="font-size: 24px; width: 24px; height: 24px; margin-bottom: 8px;"></span>
                        Produto Review
                    </a>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button id="close-modal" class="button">Cancelar</button>
                </div>
            </div>
        </div>

        <script>
            jQuery(document).ready(function($) {
                // Verificar se deve mostrar o modal automaticamente
                <?php if (isset($_GET['show_modal']) && $_GET['show_modal'] == '1'): ?>
                    // Esconder o spinner global se estiver visível
                    if (window.e1copyHideSpinner) {
                        window.e1copyHideSpinner();
                    } else {
                        var overlay = document.getElementById('e1copy-spinner-overlay');
                        if (overlay) {
                            overlay.style.display = 'none';
                        }
                    }
                    $('#post-type-modal').show();
                <?php endif; ?>

                // Abrir modal ao clicar no botão "Novo Post"
                $('#home-new-post-button').click(function(e) {
                    e.preventDefault();
                    // Esconder o spinner global se estiver visível
                    if (window.e1copyHideSpinner) {
                        window.e1copyHideSpinner();
                    } else {
                        var overlay = document.getElementById('e1copy-spinner-overlay');
                        if (overlay) {
                            overlay.style.display = 'none';
                        }
                    }
                    $('#post-type-modal').show();
                });

                // Fechar modal ao clicar no botão "Cancelar"
                $('#close-modal').click(function() {
                    $('#post-type-modal').hide();
                    // Esconder o spinner global se estiver visível
                    if (window.e1copyHideSpinner) {
                        window.e1copyHideSpinner();
                    }
                });

                // Fechar modal ao clicar fora dele
                $(window).click(function(e) {
                    if (e.target.id === 'post-type-modal') {
                        $('#post-type-modal').hide();
                        // Esconder o spinner global se estiver visível
                        if (window.e1copyHideSpinner) {
                            window.e1copyHideSpinner();
                        }
                    }
                });
            });
        </script>
    </div>
<?php
}
