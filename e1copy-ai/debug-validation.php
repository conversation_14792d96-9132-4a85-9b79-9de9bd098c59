<?php
/**
 * Script de debug para testar validação de chave
 */

// Simular ambiente WordPress
define('ABSPATH', dirname(__FILE__) . '/');
define('E1COPY_AI_VERSION', '1.0.0');

/**
 * Função de validação (cópia da função do plugin)
 */
function debug_validate_activation_key($activation_key) {
    if (empty($activation_key)) {
        return ['valid' => false, 'message' => 'Chave de ativação não fornecida'];
    }
    
    echo "🔍 Testando chave: " . substr($activation_key, 0, 10) . "...\n";
    
    // Fazer requisição para a API do dashboard
    $url = 'https://app.melhorcupom.shop/api/v1/validate';
    $data = json_encode(['api_key' => $activation_key]);
    
    echo "📡 Fazendo requisição para: $url\n";
    echo "📦 Dados enviados: $data\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'User-Agent: E1Copy-AI-Plugin/' . E1COPY_AI_VERSION
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "📊 Status HTTP: $httpCode\n";
    echo "📄 Resposta bruta: $response\n";
    
    if ($error) {
        echo "❌ Erro cURL: $error\n";
        return ['valid' => false, 'message' => 'Erro de conexão: ' . $error];
    }
    
    if ($httpCode !== 200) {
        echo "❌ Status HTTP inválido: $httpCode\n";
        return ['valid' => false, 'message' => "Erro HTTP: $httpCode"];
    }
    
    $data = json_decode($response, true);
    
    if (!$data) {
        echo "❌ Resposta JSON inválida\n";
        return ['valid' => false, 'message' => 'Resposta inválida do servidor'];
    }
    
    echo "📋 Dados decodificados: " . print_r($data, true) . "\n";
    
    if (isset($data['success']) && $data['success'] && 
        isset($data['data']['valid']) && $data['data']['valid']) {
        echo "✅ Chave válida!\n";
        return ['valid' => true, 'message' => 'Chave válida'];
    }
    
    $message = isset($data['message']) ? $data['message'] : 'Chave inválida ou expirada';
    echo "❌ Chave inválida: $message\n";
    return ['valid' => false, 'message' => $message];
}

// Testar com a chave fornecida
if (isset($argv[1])) {
    $key = $argv[1];
} else {
    echo "Digite a chave de ativação para testar: ";
    $key = trim(fgets(STDIN));
}

if (empty($key)) {
    echo "❌ Nenhuma chave fornecida\n";
    exit(1);
}

echo "🚀 Iniciando teste de validação...\n";
echo "🔑 Chave: " . substr($key, 0, 10) . "...\n\n";

$result = debug_validate_activation_key($key);

echo "\n📊 Resultado final:\n";
echo "✅ Válida: " . ($result['valid'] ? 'SIM' : 'NÃO') . "\n";
echo "💬 Mensagem: " . $result['message'] . "\n";

if ($result['valid']) {
    echo "\n🎉 Teste de validação PASSOU!\n";
    exit(0);
} else {
    echo "\n❌ Teste de validação FALHOU!\n";
    exit(1);
}
?>
