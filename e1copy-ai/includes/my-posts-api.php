<?php
if (!defined('ABSPATH')) exit; // Evita acesso direto

/**
 * Classe para gerenciar a API REST dos posts
 */
class E1Copy_My_Posts_API {
    /**
     * Namespace da API
     */
    private $namespace = 'e1copy-ai/v1';

    /**
     * Rota base para os posts
     */
    private $route = 'posts';

    /**
     * Construtor
     */
    public function __construct() {
        add_action('rest_api_init', [$this, 'register_routes']);

        // Garantir que as respostas de erro também sejam JSON
        add_filter('rest_pre_serve_request', [$this, 'ensure_proper_headers'], 10, 4);
    }

    /**
     * Garante que os cabeçalhos corretos sejam enviados para todas as respostas da API
     */
    public function ensure_proper_headers($served, $result, $request, $server) {
        // Definir cabeçalhos para CORS e tipo de conteúdo
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: X-E1Copy-API-Key, Content-Type, Authorization');
        header('Content-Type: application/json; charset=utf-8');

        // Não modificar o comportamento padrão
        return $served;
    }

    /**
     * Registra as rotas da API
     */
    public function register_routes() {
        // Rota para testar a API (não requer autenticação)
        register_rest_route($this->namespace, '/test', [
            'methods' => 'GET',
            'callback' => [$this, 'test_api'],
            'permission_callback' => '__return_true',
        ]);

        // Rota para listar todos os posts
        register_rest_route($this->namespace, '/' . $this->route, [
            'methods' => 'GET',
            'callback' => [$this, 'get_items'],
            'permission_callback' => [$this, 'get_items_permissions_check'],
        ]);

        // Rota para obter um post específico
        register_rest_route($this->namespace, '/' . $this->route . '/(?P<id>[\d]+)', [
            'methods' => WP_REST_Server::READABLE,
            'callback' => [$this, 'get_item'],
            'permission_callback' => [$this, 'get_item_permissions_check'],
            'args' => [
                'id' => [
                    'validate_callback' => function($param) {
                        return is_numeric($param);
                    }
                ],
            ],
        ]);

        // Rota para marcar um post como processado
        register_rest_route($this->namespace, '/' . $this->route . '/(?P<id>[\d]+)/mark-processed', [
            'methods' => WP_REST_Server::EDITABLE,
            'callback' => [$this, 'mark_processed'],
            'permission_callback' => [$this, 'update_item_permissions_check'],
            'args' => [
                'id' => [
                    'validate_callback' => function($param) {
                        return is_numeric($param);
                    }
                ],
            ],
        ]);

        // Rota para remover um post
        register_rest_route($this->namespace, '/' . $this->route . '/(?P<id>[\d]+)', [
            'methods' => WP_REST_Server::DELETABLE,
            'callback' => [$this, 'delete_item'],
            'permission_callback' => [$this, 'delete_item_permissions_check'],
            'args' => [
                'id' => [
                    'validate_callback' => function($param) {
                        return is_numeric($param);
                    }
                ],
            ],
        ]);
    }

    /**
     * Verifica permissões para listar posts
     */
    public function get_items_permissions_check($request) {
        // Garantir que a resposta de erro seja sempre JSON válido
        header('Content-Type: application/json; charset=utf-8');

        try {
            // Verificar se o usuário tem uma chave de API válida
            $api_key = $request->get_header('X-E1Copy-API-Key');
            $headers_received = [];

            // Registrar todos os cabeçalhos recebidos para depuração
            foreach ($request->get_headers() as $key => $value) {
                $headers_received[$key] = is_array($value) ? implode(', ', $value) : $value;
            }
            error_log('E1Copy API: Cabeçalhos recebidos: ' . json_encode($headers_received));

            // Tentar outros formatos de cabeçalho se o primeiro não existir
            if (!$api_key) {
                $api_key = $request->get_header('x-e1copy-api-key');
            }

            // Verificar no parâmetro da URL se não estiver nos cabeçalhos
            if (!$api_key && isset($request['api_key'])) {
                $api_key = $request['api_key'];
                error_log('E1Copy API: Usando api_key da URL: ' . substr($api_key, 0, 5) . '...');
            }

            // Verificar no Authorization header (para compatibilidade com alguns clientes)
            if (!$api_key) {
                $auth_header = $request->get_header('Authorization');
                if ($auth_header && strpos($auth_header, 'Bearer ') === 0) {
                    $api_key = substr($auth_header, 7);
                    error_log('E1Copy API: Usando Bearer token: ' . substr($api_key, 0, 5) . '...');
                }
            }

            if (!$api_key) {
                error_log('E1Copy API: Nenhuma chave de API fornecida');

                return new WP_Error(
                    'rest_forbidden',
                    'API Key não fornecida. Adicione o cabeçalho X-E1Copy-API-Key com sua chave de API ou use ?api_key= na URL.',
                    ['status' => 401]
                );
            }

            $valid_key = get_option('e1copy_ai_rest_api_key');
            if (!$valid_key) {
                error_log('E1Copy API: Nenhuma chave de API configurada no WordPress');

                return new WP_Error(
                    'rest_forbidden',
                    'Nenhuma chave de API configurada no WordPress. Configure uma chave nas configurações do plugin.',
                    ['status' => 401]
                );
            }

            if ($api_key !== $valid_key) {
                // Registrar erro para depuração
                error_log('E1Copy API: Chave inválida fornecida: ' . substr($api_key, 0, 5) . '... (esperada: ' . substr($valid_key, 0, 5) . '...)');

                return new WP_Error(
                    'rest_forbidden',
                    'API Key inválida. Verifique se a chave está correta nas configurações do plugin.',
                    ['status' => 401]
                );
            }

            error_log('E1Copy API: Autenticação bem-sucedida');
            return true;

        } catch (Exception $e) {
            error_log('E1Copy API Error na autenticação: ' . $e->getMessage());

            return new WP_Error(
                'rest_error',
                'Erro ao processar a autenticação: ' . $e->getMessage(),
                ['status' => 500]
            );
        }
    }

    /**
     * Verifica permissões para obter um post específico
     */
    public function get_item_permissions_check($request) {
        return $this->get_items_permissions_check($request);
    }

    /**
     * Verifica permissões para atualizar um post
     */
    public function update_item_permissions_check($request) {
        return $this->get_items_permissions_check($request);
    }

    /**
     * Verifica permissões para remover um post
     */
    public function delete_item_permissions_check($request) {
        return $this->get_items_permissions_check($request);
    }

    /**
     * Endpoint de teste para verificar se a API está funcionando
     */
    public function test_api() {
        // Garantir que a resposta seja sempre JSON válido
        header('Content-Type: application/json; charset=utf-8');

        $response = [
            'success' => true,
            'message' => 'API E1Copy está funcionando corretamente!',
            'time' => current_time('mysql'),
            'version' => '1.0'
        ];

        // Registrar a chamada para depuração
        error_log('E1Copy API: Endpoint de teste acessado em ' . current_time('mysql'));

        return rest_ensure_response($response);
    }

    /**
     * Obtém a lista de todos os posts
     */
    public function get_items($request) {
        // Garantir que a resposta seja sempre JSON válido
        header('Content-Type: application/json; charset=utf-8');

        try {
            // Parâmetros de paginação
            $per_page = isset($request['per_page']) ? (int) $request['per_page'] : 10;
            $page = isset($request['page']) ? (int) $request['page'] : 1;
            $offset = ($page - 1) * $per_page;

            // Parâmetros de filtro
            $status = isset($request['status']) ? sanitize_text_field($request['status']) : '';
            $processed = isset($request['processed']) ? filter_var($request['processed'], FILTER_VALIDATE_BOOLEAN) : null;

            // Registrar a chamada para depuração
            error_log('E1Copy API: Listando posts com filtros - status: ' . $status . ', processed: ' . ($processed === null ? 'null' : ($processed ? 'true' : 'false')));

            // Obter posts com base nos filtros
            $posts = $this->get_filtered_posts($status, $processed, $per_page, $offset);

            // Contar total de posts para paginação
            $total_posts = $this->count_filtered_posts($status, $processed);

            // Preparar resposta
            $response = [
                'success' => true,
                'posts' => $posts ?: [], // Garantir que sempre seja um array, mesmo que vazio
                'total' => $total_posts,
                'pages' => ceil($total_posts / $per_page),
                'page' => $page,
                'per_page' => $per_page,
            ];

            return rest_ensure_response($response);
        } catch (Exception $e) {
            // Em caso de erro, retornar uma resposta de erro em formato JSON
            error_log('E1Copy API Error: ' . $e->getMessage());

            return rest_ensure_response([
                'success' => false,
                'error' => 'Erro ao processar a requisição: ' . $e->getMessage(),
                'code' => 'api_error'
            ]);
        }
    }

    /**
     * Obtém um post específico pelo ID
     */
    public function get_item($request) {
        $id = (int) $request['id'];
        $post = e1copy_ai_get_my_post($id);

        if (!$post) {
            return new WP_Error('rest_post_not_found', 'Post não encontrado', ['status' => 404]);
        }

        // Converter categorias para string simples e adicionar informações de categorias
        if (!empty($post['categories'])) {
            $categories = $post['categories'];
            // Se for um array, pegar apenas o primeiro ID como string
            if (is_array($categories) && !empty($categories)) {
                $post['categories'] = (string) $categories[0];
                $first_cat_id = $categories[0];
            } elseif (!empty($categories)) {
                $post['categories'] = (string) $categories;
                $first_cat_id = $categories;
            } else {
                $post['categories'] = '';
                $first_cat_id = null;
            }

            // Adicionar nomes das categorias
            $post['category_names'] = [];
            if ($first_cat_id) {
                $category = get_category($first_cat_id);
                if ($category) {
                    $post['category_names'][] = $category->name;
                }
            }
        }

        return rest_ensure_response($post);
    }

    /**
     * Remove um post
     */
    public function delete_item($request) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'e1copy_my_posts';

        $id = (int) $request['id'];
        $post = e1copy_ai_get_my_post($id);

        if (!$post) {
            return new WP_Error('rest_post_not_found', 'Post não encontrado', ['status' => 404]);
        }

        // Remover o post
        $result = e1copy_ai_delete_my_post($id);

        // Log para depuração
        error_log('E1Copy API Delete: Removendo post ID ' . $id);

        if (!$result) {
            return new WP_Error('rest_delete_failed', 'Falha ao remover o post', ['status' => 500]);
        }

        return rest_ensure_response([
            'success' => true,
            'message' => 'Post removido com sucesso',
            'id' => $id
        ]);
    }

    /**
     * Marca um post como processado
     */
    public function mark_processed($request) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'e1copy_my_posts';

        $id = (int) $request['id'];
        $post = e1copy_ai_get_my_post($id);

        if (!$post) {
            return new WP_Error('rest_post_not_found', 'Post não encontrado', ['status' => 404]);
        }

        // Log do estado atual do post
        error_log('E1Copy API Mark: Estado atual do post ID ' . $id . ': processed = ' . $post['processed']);

        // Sempre marcamos como processado (valor 1 no banco)
        $db_value = 1;
        $result = $wpdb->update(
            $table_name,
            ['processed' => $db_value],
            ['id' => $id]
        );

        // Log para depuração
        error_log('E1Copy API Mark: Marcando post ID ' . $id . ' como processado (processed = 1 no banco)');

        // Log detalhado para depuração
        error_log('E1Copy API Mark: Query SQL: ' . $wpdb->last_query);
        error_log('E1Copy API Mark: Resultado da atualização: ' . ($result !== false ? 'Sucesso' : 'Falha'));

        // Verificar se o post foi atualizado corretamente
        $updated_post = e1copy_ai_get_my_post($id);
        error_log('E1Copy API Mark: Estado após atualização do post ID ' . $id . ': processed = ' . $updated_post['processed']);

        if ($result === false) {
            return new WP_Error('rest_update_failed', 'Falha ao atualizar o post', ['status' => 500]);
        }

        return rest_ensure_response([
            'success' => true,
            'message' => 'Post marcado como processado',
            'id' => $id
        ]);
    }

    /**
     * Obtém posts filtrados
     */
    private function get_filtered_posts($status = '', $processed = null, $limit = 10, $offset = 0) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'e1copy_my_posts';

        $where = [];
        $values = [];

        if ($status) {
            $where[] = 'status = %s';
            $values[] = $status;
        }

        // Verificar se o parâmetro processed foi fornecido
        if ($processed !== null) {
            $where[] = 'processed = %d';
            $values[] = $processed ? 1 : 0;
            error_log('E1Copy API: Filtrando posts com processed = ' . ($processed ? '1 (processados)' : '0 (não processados)'));
        } else {
            // Por padrão, se nenhum valor for especificado, mostrar posts não processados
            $where[] = 'processed = %d';
            $values[] = 0;
            error_log('E1Copy API: Nenhum valor de processed fornecido, mostrando posts não processados (processed = 0)');
        }

        // Log detalhado para depuração
        error_log('E1Copy API: Consulta WHERE: ' . implode(' AND ', $where));
        error_log('E1Copy API: Valores: ' . print_r($values, true));

        // Verificar se há posts com processed = 0 para depuração
        $count_unprocessed = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE processed = 0");
        error_log('E1Copy API: Total de posts com processed = 0 (não processados): ' . $count_unprocessed);

        // Verificar se há posts com processed = 1 para depuração
        $count_processed = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE processed = 1");
        error_log('E1Copy API: Total de posts com processed = 1 (processados): ' . $count_processed);

        $where_clause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

        $query = $wpdb->prepare(
            "SELECT * FROM $table_name $where_clause ORDER BY created_at DESC LIMIT %d OFFSET %d",
            array_merge($values, [$limit, $offset])
        );

        // Log da consulta SQL para depuração
        error_log('E1Copy API: Consulta SQL: ' . $query);

        $posts = $wpdb->get_results($query, ARRAY_A);

        // Log do número de posts encontrados
        error_log('E1Copy API: Número de posts encontrados: ' . count($posts));

        // Deserializar categorias e converter para string simples
        foreach ($posts as &$post) {
            $categories = maybe_unserialize($post['categories']);
            // Se for um array, pegar apenas o primeiro ID como string
            if (is_array($categories) && !empty($categories)) {
                $post['categories'] = (string) $categories[0];
            } elseif (!empty($categories)) {
                $post['categories'] = (string) $categories;
            } else {
                $post['categories'] = '';
            }
        }

        return $posts;
    }

    /**
     * Conta o total de posts com base nos filtros
     */
    private function count_filtered_posts($status = '', $processed = null) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'e1copy_my_posts';

        $where = [];
        $values = [];

        if ($status) {
            $where[] = 'status = %s';
            $values[] = $status;
        }

        // Verificar se o parâmetro processed foi fornecido
        if ($processed !== null) {
            $where[] = 'processed = %d';
            $values[] = $processed ? 1 : 0;
            error_log('E1Copy API Count: Filtrando posts com processed = ' . ($processed ? '1 (processados)' : '0 (não processados)'));
        } else {
            // Por padrão, se nenhum valor for especificado, mostrar posts não processados
            $where[] = 'processed = %d';
            $values[] = 0;
            error_log('E1Copy API Count: Nenhum valor de processed fornecido, mostrando posts não processados (processed = 0)');
        }

        $where_clause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

        $query = "SELECT COUNT(*) FROM $table_name $where_clause";
        if (!empty($values)) {
            $query = $wpdb->prepare($query, $values);
        }

        return (int) $wpdb->get_var($query);
    }
}

// Inicializar a API
new E1Copy_My_Posts_API();
