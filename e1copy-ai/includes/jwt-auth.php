<?php
if (!defined('ABSPATH')) exit; // Evita acesso direto

/**
 * Implementação de autenticação JWT para a API E1Copy
 */
class E1Copy_JWT_Auth {
    /**
     * Chave secreta para assinar os tokens JWT
     */
    private $secret_key;
    
    /**
     * Tempo de expiração do token em segundos (padrão: 24 horas)
     */
    private $expiration = 86400;
    
    /**
     * Construtor
     */
    public function __construct() {
        // Definir a chave secreta (usando a chave de API existente ou gerando uma nova)
        $this->secret_key = get_option('e1copy_ai_jwt_secret');
        if (!$this->secret_key) {
            $this->secret_key = $this->generate_secret_key();
            update_option('e1copy_ai_jwt_secret', $this->secret_key);
        }
        
        // Registrar endpoints da API
        add_action('rest_api_init', [$this, 'register_routes']);
    }
    
    /**
     * Gera uma chave secreta aleatória
     */
    private function generate_secret_key() {
        return bin2hex(random_bytes(32)); // 64 caracteres hexadecimais
    }
    
    /**
     * Registra as rotas da API para autenticação JWT
     */
    public function register_routes() {
        $namespace = 'e1copy-ai/v1';
        
        // Endpoint para obter um token JWT
        register_rest_route($namespace, '/token', [
            'methods' => 'POST',
            'callback' => [$this, 'generate_token'],
            'permission_callback' => '__return_true',
        ]);
        
        // Endpoint para validar um token JWT
        register_rest_route($namespace, '/token/validate', [
            'methods' => 'POST',
            'callback' => [$this, 'validate_token'],
            'permission_callback' => '__return_true',
        ]);
    }
    
    /**
     * Gera um token JWT com base nas credenciais fornecidas
     */
    public function generate_token($request) {
        // Adicionar cabeçalhos CORS
        $this->add_cors_headers();
        
        // Obter parâmetros da requisição
        $username = $request->get_param('username');
        $password = $request->get_param('password');
        $api_key = $request->get_param('api_key');
        
        // Verificar se foi fornecida uma chave de API
        if ($api_key) {
            $valid_key = get_option('e1copy_ai_rest_api_key');
            if (!$valid_key || trim($api_key) !== trim($valid_key)) {
                $response = [
                    'success' => false,
                    'message' => 'Chave de API inválida',
                ];
                return $this->json_response($response, 401);
            }
            
            // Gerar token com base na chave de API
            $token = $this->create_token('api_key', $api_key);
            
            $response = [
                'success' => true,
                'token' => $token,
                'expires_in' => $this->expiration,
            ];
            return $this->json_response($response);
        }
        
        // Verificar se foram fornecidas credenciais de usuário
        if ($username && $password) {
            // Autenticar usuário
            $user = wp_authenticate($username, $password);
            
            if (is_wp_error($user)) {
                $response = [
                    'success' => false,
                    'message' => 'Credenciais inválidas',
                ];
                return $this->json_response($response, 401);
            }
            
            // Verificar se o usuário tem permissão para acessar a API
            if (!user_can($user, 'manage_options')) {
                $response = [
                    'success' => false,
                    'message' => 'Usuário não tem permissão para acessar a API',
                ];
                return $this->json_response($response, 403);
            }
            
            // Gerar token com base no usuário
            $token = $this->create_token('user', $user->ID);
            
            $response = [
                'success' => true,
                'token' => $token,
                'user_id' => $user->ID,
                'user_email' => $user->user_email,
                'user_display_name' => $user->display_name,
                'expires_in' => $this->expiration,
            ];
            return $this->json_response($response);
        }
        
        // Se não foram fornecidas credenciais válidas
        $response = [
            'success' => false,
            'message' => 'Credenciais não fornecidas. Forneça username/password ou api_key.',
        ];
        return $this->json_response($response, 400);
    }
    
    /**
     * Valida um token JWT
     */
    public function validate_token($request) {
        // Adicionar cabeçalhos CORS
        $this->add_cors_headers();
        
        // Obter token da requisição
        $token = $request->get_param('token');
        if (!$token) {
            $token = $this->get_token_from_headers();
        }
        
        if (!$token) {
            $response = [
                'success' => false,
                'message' => 'Token não fornecido',
            ];
            return $this->json_response($response, 400);
        }
        
        // Validar token
        $is_valid = $this->validate_jwt($token);
        if (!$is_valid) {
            $response = [
                'success' => false,
                'message' => 'Token inválido ou expirado',
            ];
            return $this->json_response($response, 401);
        }
        
        // Decodificar token para obter informações
        $payload = $this->decode_jwt($token);
        
        $response = [
            'success' => true,
            'message' => 'Token válido',
            'data' => $payload,
        ];
        return $this->json_response($response);
    }
    
    /**
     * Cria um token JWT
     */
    public function create_token($type, $identifier) {
        // Cabeçalho do token
        $header = [
            'alg' => 'HS256',
            'typ' => 'JWT',
        ];
        
        // Payload do token
        $payload = [
            'iss' => get_site_url(), // Emissor
            'iat' => time(), // Emitido em
            'exp' => time() + $this->expiration, // Expira em
            'type' => $type, // Tipo de autenticação
            'id' => $identifier, // Identificador (ID do usuário ou chave de API)
        ];
        
        // Codificar cabeçalho e payload
        $header_encoded = $this->base64url_encode(json_encode($header));
        $payload_encoded = $this->base64url_encode(json_encode($payload));
        
        // Criar assinatura
        $signature = hash_hmac('sha256', "$header_encoded.$payload_encoded", $this->secret_key, true);
        $signature_encoded = $this->base64url_encode($signature);
        
        // Montar token
        $token = "$header_encoded.$payload_encoded.$signature_encoded";
        
        return $token;
    }
    
    /**
     * Valida um token JWT
     */
    public function validate_jwt($token) {
        // Dividir token em partes
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }
        
        list($header_encoded, $payload_encoded, $signature_encoded) = $parts;
        
        // Verificar assinatura
        $signature = $this->base64url_decode($signature_encoded);
        $expected_signature = hash_hmac('sha256', "$header_encoded.$payload_encoded", $this->secret_key, true);
        
        if (!hash_equals($signature, $expected_signature)) {
            return false;
        }
        
        // Verificar expiração
        $payload = json_decode($this->base64url_decode($payload_encoded), true);
        if (!isset($payload['exp']) || $payload['exp'] < time()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Decodifica um token JWT
     */
    public function decode_jwt($token) {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return null;
        }
        
        list($header_encoded, $payload_encoded, $signature_encoded) = $parts;
        
        $payload = json_decode($this->base64url_decode($payload_encoded), true);
        
        return $payload;
    }
    
    /**
     * Obtém o token JWT dos cabeçalhos da requisição
     */
    public function get_token_from_headers() {
        // Verificar cabeçalho Authorization
        $auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
        if (!$auth_header) {
            $auth_header = isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION']) ? $_SERVER['REDIRECT_HTTP_AUTHORIZATION'] : '';
        }
        
        // Verificar formato Bearer token
        if ($auth_header && preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            return $matches[1];
        }
        
        return null;
    }
    
    /**
     * Verifica se um token JWT é válido e retorna o payload
     */
    public function authenticate_request() {
        $token = $this->get_token_from_headers();
        if (!$token) {
            return false;
        }
        
        if (!$this->validate_jwt($token)) {
            return false;
        }
        
        return $this->decode_jwt($token);
    }
    
    /**
     * Codifica uma string em base64url
     */
    private function base64url_encode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Decodifica uma string em base64url
     */
    private function base64url_decode($data) {
        return base64_decode(strtr($data, '-_', '+/'));
    }
    
    /**
     * Adiciona cabeçalhos CORS
     */
    private function add_cors_headers() {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE, PATCH');
        header('Access-Control-Allow-Headers: Authorization, Content-Type, X-Requested-With');
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 3600');
        header('Content-Type: application/json; charset=utf-8');
    }
    
    /**
     * Retorna uma resposta JSON
     */
    private function json_response($data, $status_code = 200) {
        status_header($status_code);
        echo json_encode($data);
        exit;
    }
}

// Inicializar a classe
$e1copy_jwt_auth = new E1Copy_JWT_Auth();
