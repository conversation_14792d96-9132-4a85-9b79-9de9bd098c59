<?php
if (!defined('ABSPATH')) {
    exit;
}

function e1copy_ai_install() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    $table_name = $wpdb->prefix . 'e1copy_page_stats';

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        page_id bigint(20) NOT NULL,
        visit_date datetime NOT NULL,
        ip_address varchar(45) NOT NULL,
        user_agent text NOT NULL,
        referrer text,
        PRIMARY KEY  (id),
        KEY page_id (page_id),
        KEY visit_date (visit_date)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}

function e1copy_ai_uninstall() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_page_stats';
    $wpdb->query("DROP TABLE IF EXISTS $table_name");
}

// Registrar hooks de ativação e desativação
register_activation_hook(E1COPY_AI_PLUGIN_FILE, 'e1copy_ai_install');
register_uninstall_hook(E1COPY_AI_PLUGIN_FILE, 'e1copy_ai_uninstall'); 