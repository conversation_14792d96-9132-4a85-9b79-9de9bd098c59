<?php
if (!defined('ABSPATH')) exit; // Evita acesso direto

/**
 * Cria a tabela para armazenar os dados dos posts
 */
function e1copy_ai_create_my_posts_table() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    $table_name = $wpdb->prefix . 'e1copy_my_posts';

    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id bigint(20) NOT NULL AUTO_INCREMENT,
        post_type varchar(50) DEFAULT 'blog',
        title varchar(255) NOT NULL,
        keyword varchar(255) DEFAULT '',
        categories text DEFAULT '',
        tags text DEFAULT '',
        status varchar(20) DEFAULT 'draft',
        image_source varchar(50) DEFAULT 'pexels',
        subtitles_count int(11) DEFAULT 5,
        has_summary varchar(3) DEFAULT 'no',
        has_conclusion varchar(3) DEFAULT 'no',
        has_faq varchar(3) DEFAULT 'no',
        has_internal_link varchar(3) DEFAULT 'no',
        youtube_video text DEFAULT '',
        product_name varchar(255) DEFAULT '',
        product_description longtext DEFAULT '',
        product_images longtext DEFAULT '',
        post_cover_image text DEFAULT '',
        post_cover_image_id bigint(20) DEFAULT 0,
        affiliate_id bigint(20) DEFAULT 0,
        processed tinyint(1) DEFAULT 0,
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY  (id)
    ) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);

    // Verificar se as colunas necessárias existem
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $column_names = [];
    foreach ($columns as $column) {
        $column_names[] = $column->Field;
    }

    // Adicionar colunas se não existirem
    if (!in_array('post_type', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN post_type varchar(50) DEFAULT 'blog' AFTER id");
        error_log('E1Copy Handler: Coluna post_type adicionada à tabela');
    }

    if (!in_array('product_name', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN product_name varchar(255) DEFAULT ''");
        error_log('E1Copy Handler: Coluna product_name adicionada à tabela');
    }

    if (!in_array('product_description', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN product_description longtext DEFAULT ''");
        error_log('E1Copy Handler: Coluna product_description adicionada à tabela');
    }



    if (!in_array('affiliation_link', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN affiliation_link text DEFAULT ''");
        error_log('E1Copy Handler: Coluna affiliation_link adicionada à tabela');
    }

    if (!in_array('cta_image', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN cta_image text DEFAULT ''");
        error_log('E1Copy Handler: Coluna cta_image adicionada à tabela');
    }

    if (!in_array('product_images', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN product_images longtext DEFAULT ''");
        error_log('E1Copy Handler: Coluna product_images adicionada à tabela');
    }

    if (!in_array('post_cover_image', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN post_cover_image text DEFAULT ''");
        error_log('E1Copy Handler: Coluna post_cover_image adicionada à tabela');
    }

    if (!in_array('post_cover_image_id', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN post_cover_image_id bigint(20) DEFAULT 0");
        error_log('E1Copy Handler: Coluna post_cover_image_id adicionada à tabela');
    }

    if (!in_array('affiliate_id', $column_names)) {
        $wpdb->query("ALTER TABLE $table_name ADD COLUMN affiliate_id bigint(20) DEFAULT 0");
        error_log('E1Copy Handler: Coluna affiliate_id adicionada à tabela');
    }
}

/**
 * Salva um novo post na tabela
 */
function e1copy_ai_save_my_post($data) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_my_posts';

    // Definir o tipo de post (padrão: 'blog')
    $post_type = isset($data['post_type']) ? $data['post_type'] : 'blog';

    // Preparar dados para inserção
    $post_data = [
        'post_type' => $post_type,
        'title' => $data['title'],
        'keyword' => $data['keyword'],
        'categories' => maybe_serialize($data['categories']),
        'tags' => $data['tags'],
        'status' => ($post_type === 'product') ? 'publish' : $data['status'], // Status sempre "publish" para produtos
        'processed' => isset($data['processed']) ? $data['processed'] : 0,
    ];

    // Adicionar campos específicos com base no tipo de post
    if ($post_type === 'blog') {
        $post_data = array_merge($post_data, [
            'image_source' => isset($data['image_source']) ? $data['image_source'] : 'pexels',
            'subtitles_count' => isset($data['subtitles_count']) ? $data['subtitles_count'] : 5,
            'has_summary' => isset($data['has_summary']) ? $data['has_summary'] : 'no',
            'has_conclusion' => isset($data['has_conclusion']) ? $data['has_conclusion'] : 'no',
            'has_faq' => isset($data['has_faq']) ? $data['has_faq'] : 'no',
            'has_internal_link' => isset($data['has_internal_link']) ? $data['has_internal_link'] : 'no',
        ]);
    } elseif ($post_type === 'product') {
        $post_data = array_merge($post_data, [
            'product_name' => isset($data['product_name']) ? $data['product_name'] : '',
            'product_description' => isset($data['product_description']) ? $data['product_description'] : '',
            'product_images' => isset($data['product_images']) ? $data['product_images'] : json_encode([]),
            'post_cover_image' => isset($data['post_cover_image']) ? $data['post_cover_image'] : '',
            'post_cover_image_id' => isset($data['post_cover_image_id']) ? intval($data['post_cover_image_id']) : 0,
            'affiliate_id' => isset($data['affiliate_id']) ? intval($data['affiliate_id']) : 0,
        ]);
    }

    // Campos comuns a ambos os tipos
    $post_data = array_merge($post_data, [
        'youtube_video' => isset($data['youtube_video']) ? $data['youtube_video'] : '',
        'cta_text' => isset($data['cta_text']) ? $data['cta_text'] : '',
        'cta_link' => isset($data['cta_link']) ? $data['cta_link'] : '',
    ]);

    // Log para depuração
    $post_id = isset($data['id']) ? $data['id'] : 'novo';
    error_log('E1Copy Handler: Salvando post ID ' . $post_id . ' com processed = ' . $post_data['processed']);
    error_log('E1Copy Handler: Tipo de post: ' . $post_type);
    error_log('E1Copy Handler: Dados do post: ' . print_r($post_data, true));

    // Verificar se a tabela tem todas as colunas necessárias
    $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
    $column_names = [];
    foreach ($columns as $column) {
        $column_names[] = $column->Field;
    }

    // Verificar colunas necessárias
    $required_columns = ['post_type', 'product_name', 'product_description', 'product_images', 'post_cover_image', 'post_cover_image_id', 'affiliate_id'];
    $missing_columns = [];

    foreach ($required_columns as $column) {
        if (!in_array($column, $column_names)) {
            $missing_columns[] = $column;
        }
    }

    // Se houver colunas faltando, tentar adicioná-las
    if (!empty($missing_columns)) {
        error_log('E1Copy Handler: Colunas faltando na tabela: ' . implode(', ', $missing_columns));

        // Forçar a atualização da tabela
        e1copy_ai_create_my_posts_table();

        // Verificar novamente se as colunas foram adicionadas
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name");
        $column_names = [];
        foreach ($columns as $column) {
            $column_names[] = $column->Field;
        }

        // Verificar se ainda há colunas faltando
        $still_missing = [];
        foreach ($required_columns as $column) {
            if (!in_array($column, $column_names)) {
                $still_missing[] = $column;
            }
        }

        if (!empty($still_missing)) {
            error_log('E1Copy Handler: Não foi possível adicionar as colunas: ' . implode(', ', $still_missing));
            return false;
        }
    }

    // Inserir ou atualizar
    if (isset($data['id']) && $data['id'] > 0) {
        // Log da consulta SQL para depuração
        $wpdb->show_errors();
        $result = $wpdb->update(
            $table_name,
            $post_data,
            ['id' => $data['id']]
        );

        // Log do resultado da atualização
        if ($result === false) {
            error_log('E1Copy Handler: Erro ao atualizar post ID ' . $data['id'] . ': ' . $wpdb->last_error);
            error_log('E1Copy Handler: Última consulta SQL: ' . $wpdb->last_query);
        } else {
            error_log('E1Copy Handler: Post ID ' . $data['id'] . ' atualizado com sucesso. Linhas afetadas: ' . $result);
        }

        return $data['id'];
    } else {
        // Log da consulta SQL para depuração
        $wpdb->show_errors();
        $result = $wpdb->insert($table_name, $post_data);

        if ($result === false) {
            error_log('E1Copy Handler: Erro ao inserir novo post: ' . $wpdb->last_error);
            error_log('E1Copy Handler: Última consulta SQL: ' . $wpdb->last_query);
            return false;
        } else {
            error_log('E1Copy Handler: Novo post criado com ID ' . $wpdb->insert_id);
            return $wpdb->insert_id;
        }
    }
}

/**
 * Obtém um post pelo ID
 */
function e1copy_ai_get_my_post($id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_my_posts';

    $post = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id),
        ARRAY_A
    );

    if ($post) {
        // Deserializar categorias
        $post['categories'] = maybe_unserialize($post['categories']);
    }

    return $post;
}

/**
 * Obtém todos os posts
 */
function e1copy_ai_get_all_my_posts() {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_my_posts';

    $posts = $wpdb->get_results("SELECT * FROM $table_name ORDER BY created_at DESC", ARRAY_A);

    // Deserializar categorias para cada post
    foreach ($posts as &$post) {
        $post['categories'] = maybe_unserialize($post['categories']);
    }

    return $posts;
}

/**
 * Exclui um post pelo ID
 */
function e1copy_ai_delete_my_post($id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_my_posts';

    return $wpdb->delete($table_name, ['id' => $id]);
}

// Registrar hook de ativação para criar a tabela
add_action('init', 'e1copy_ai_create_my_posts_table');
