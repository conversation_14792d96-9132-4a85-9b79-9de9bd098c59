<?php
if (!defined('ABSPATH')) exit; // Evita acesso direto

/**
 * Classe para autenticação na API REST nativa do WordPress
 *
 * Esta classe permite usar a autenticação via chave de API do E1Copy
 * para acessar endpoints da API REST nativa do WordPress, como /wp/v2/posts
 */
class E1Copy_WP_API_Auth {
    /**
     * Construtor
     */
    public function __construct() {
        // Adicionar filtro para autenticação na API REST nativa do WordPress
        add_filter('rest_authentication_errors', [$this, 'authenticate_wp_api'], 20);

        // Adicionar filtro para determinar as capacidades do usuário
        add_filter('rest_pre_dispatch', [$this, 'set_current_user'], 10, 3);
    }

    /**
     * Autentica requisições para a API REST nativa do WordPress
     *
     * @param WP_Error|null|bool $result Resultado da autenticação atual
     * @return WP_Error|null|bool Resultado da autenticação
     */
    public function authenticate_wp_api($result) {
        // Se já autenticado ou erro, retornar
        if ($result !== null) {
            return $result;
        }

        // Verificar se é uma requisição para a API REST nativa do WordPress
        if (!strpos($_SERVER['REQUEST_URI'], '/wp-json/wp/')) {
            return $result;
        }

        // Registrar informações de depuração
        $this->log_debug_info('Tentando autenticar requisição para API REST nativa do WordPress: ' . $_SERVER['REQUEST_URI']);

        // Tentar autenticar com chave de API
        $api_key_result = $this->authenticate_with_api_key();
        if ($api_key_result === true) {
            $this->log_debug_info('Autenticação bem-sucedida com chave de API');
            return true;
        }

        // Tentar autenticar com Basic Auth
        $basic_auth_result = $this->authenticate_with_basic_auth();
        if ($basic_auth_result === true) {
            $this->log_debug_info('Autenticação bem-sucedida com Basic Auth');
            return true;
        }

        // Se chegou aqui, não foi possível autenticar
        $this->log_debug_info('Não foi possível autenticar com chave de API nem com Basic Auth');

        // Mas não vamos bloquear o acesso, apenas deixamos para o WordPress decidir
        return $result;
    }

    /**
     * Registra informações de depuração
     *
     * @param string $message Mensagem a ser registrada
     */
    private function log_debug_info($message) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('E1Copy WP API Auth: ' . $message);
        }
    }



    /**
     * Tenta autenticar com chave de API
     *
     * @return bool True se autenticado, false caso contrário
     */
    private function authenticate_with_api_key() {
        // Verificar se a licença do E1Copy AI está ativa
        if (!e1copy_ai_is_activated()) {
            $this->log_debug_info('Licença do E1Copy AI inválida ou suspensa');
            return false;
        }

        // Obter chave de API dos cabeçalhos ou parâmetros
        $api_key = $this->get_api_key_from_request();
        if (!$api_key) {
            $this->log_debug_info('Nenhuma chave de API encontrada na requisição');
            return false;
        }

        $this->log_debug_info('Chave de API encontrada: ' . substr($api_key, 0, 5) . '...');

        // Verificar se a chave é válida
        $valid_key = get_option('e1copy_ai_rest_api_key');
        if (!$valid_key) {
            $this->log_debug_info('Nenhuma chave de API configurada no WordPress');
            return false;
        }

        if (trim($api_key) !== trim($valid_key)) {
            $this->log_debug_info('Chave de API inválida. Esperada: ' . substr(trim($valid_key), 0, 5) . '..., Recebida: ' . substr(trim($api_key), 0, 5) . '...');
            return false;
        }

        $this->log_debug_info('Chave de API válida');

        // Se a chave for válida, definir usuário como administrador
        $admin_users = get_users(['role' => 'administrator', 'number' => 1]);
        if (empty($admin_users)) {
            $this->log_debug_info('Nenhum usuário administrador encontrado');
            return false;
        }

        wp_set_current_user($admin_users[0]->ID);
        $this->log_debug_info('Usuário administrador definido: ID ' . $admin_users[0]->ID . ', Login: ' . $admin_users[0]->user_login);
        return true;
    }

    /**
     * Tenta autenticar com Basic Auth
     *
     * @return bool True se autenticado, false caso contrário
     */
    private function authenticate_with_basic_auth() {
        // Obter credenciais Basic Auth
        $auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
        if (!$auth_header) {
            $auth_header = isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION']) ? $_SERVER['REDIRECT_HTTP_AUTHORIZATION'] : '';
        }

        // Verificar se é um cabeçalho Basic Auth
        if (!$auth_header || strpos($auth_header, 'Basic ') !== 0) {
            $this->log_debug_info('Nenhum cabeçalho Basic Auth encontrado');
            return false;
        }

        // Decodificar credenciais
        $auth_data = base64_decode(substr($auth_header, 6));
        if (!$auth_data || strpos($auth_data, ':') === false) {
            $this->log_debug_info('Credenciais Basic Auth inválidas');
            return false;
        }

        list($username, $password) = explode(':', $auth_data, 2);

        // Autenticar usuário
        $user = wp_authenticate($username, $password);
        if (is_wp_error($user)) {
            $this->log_debug_info('Falha na autenticação Basic Auth: credenciais inválidas');
            return false;
        }

        // Verificar se o usuário tem permissões adequadas (pelo menos editor)
        if (!user_can($user, 'edit_posts')) {
            $this->log_debug_info('Usuário não tem permissões suficientes para acessar a API');
            return false;
        }

        // Definir o usuário atual
        wp_set_current_user($user->ID);
        $this->log_debug_info('Usuário autenticado via Basic Auth: ID ' . $user->ID . ', Login: ' . $user->user_login);
        return true;
    }

    /**
     * Obtém a chave de API da requisição
     *
     * @return string|null Chave de API ou null se não encontrada
     */
    private function get_api_key_from_request() {
        // Verificar no cabeçalho X-E1Copy-API-Key (várias variações)
        $header_variations = [
            'HTTP_X_E1COPY_API_KEY',
            'HTTP_X_E1COPY_API_KEY',
            'HTTP_X_E1COPY_APIKEY',
            'HTTP_E1COPY_API_KEY',
            'HTTP_E1COPY_APIKEY'
        ];

        foreach ($header_variations as $header) {
            if (isset($_SERVER[$header])) {
                return $_SERVER[$header];
            }
        }

        // Verificar no parâmetro da URL
        if (isset($_GET['api_key'])) {
            return $_GET['api_key'];
        }

        // Verificar no Authorization header (Bearer token)
        $auth_header = isset($_SERVER['HTTP_AUTHORIZATION']) ? $_SERVER['HTTP_AUTHORIZATION'] : '';
        if (!$auth_header) {
            $auth_header = isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION']) ? $_SERVER['REDIRECT_HTTP_AUTHORIZATION'] : '';
        }

        if ($auth_header && preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            return $matches[1];
        }

        // Verificar em outros cabeçalhos comuns
        $other_headers = [
            'HTTP_API_KEY',
            'HTTP_APIKEY',
            'HTTP_KEY',
            'HTTP_TOKEN',
            'HTTP_X_API_KEY'
        ];

        foreach ($other_headers as $header) {
            if (isset($_SERVER[$header])) {
                return $_SERVER[$header];
            }
        }

        // Verificar em todos os cabeçalhos (caso insensível)
        foreach ($_SERVER as $key => $value) {
            if (strpos($key, 'HTTP_') === 0) {
                $header_name = str_replace('HTTP_', '', $key);
                if (stripos($header_name, 'API_KEY') !== false ||
                    stripos($header_name, 'APIKEY') !== false ||
                    stripos($header_name, 'TOKEN') !== false) {
                    return $value;
                }
            }
        }

        return null;
    }

    /**
     * Define o usuário atual para a requisição da API
     *
     * @param mixed $result Resultado da requisição
     * @param WP_REST_Server $server Servidor REST
     * @param WP_REST_Request $request Requisição REST
     * @return mixed Resultado da requisição
     */
    public function set_current_user($result, $server, $request) {
        // Verificar se é uma requisição para a API REST nativa do WordPress
        if (!strpos($request->get_route(), '/wp/v2/')) {
            return $result;
        }

        $this->log_debug_info('Verificando usuário para rota: ' . $request->get_route());

        // Se o usuário já está definido, não fazer nada
        if (is_user_logged_in()) {
            $current_user = wp_get_current_user();
            $this->log_debug_info('Usuário já autenticado: ID ' . $current_user->ID . ', Login: ' . $current_user->user_login);
            return $result;
        }

        $this->log_debug_info('Nenhum usuário autenticado, tentando autenticar novamente');

        // Tentar autenticar novamente
        $this->authenticate_wp_api(null);

        // Verificar se a autenticação foi bem-sucedida
        if (is_user_logged_in()) {
            $current_user = wp_get_current_user();
            $this->log_debug_info('Autenticação bem-sucedida: ID ' . $current_user->ID . ', Login: ' . $current_user->user_login);
        } else {
            $this->log_debug_info('Falha na autenticação');
        }

        return $result;
    }
}

// Inicializar a classe
$e1copy_wp_api_auth = new E1Copy_WP_API_Auth();
