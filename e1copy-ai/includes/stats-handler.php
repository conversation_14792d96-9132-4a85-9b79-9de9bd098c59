<?php
if (!defined('ABSPATH')) {
    exit;
}

function e1copy_ai_record_visit($page_id) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_page_stats';

    error_log('Registrando visita para a página: ' . $page_id);

    $data = array(
        'page_id' => $page_id,
        'visit_date' => current_time('mysql'),
        'ip_address' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'],
        'referrer' => isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : ''
    );

    $wpdb->insert($table_name, $data);
    if ($wpdb->last_error) {
        error_log('Erro MySQL: ' . $wpdb->last_error);
    }
}

function e1copy_ai_get_page_stats($page_id, $period = '30') {
    global $wpdb;
    $table_name = $wpdb->prefix . 'e1copy_page_stats';

    // Total de visitas
    $total_visits = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name WHERE page_id = %d",
        $page_id
    ));

    // Visitas nos últimos X dias
    $recent_visits = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name
        WHERE page_id = %d
        AND visit_date >= DATE_SUB(NOW(), INTERVAL %d DAY)",
        $page_id,
        $period
    ));

    // Visitas por dia (últimos 7 dias)
    $daily_stats = $wpdb->get_results($wpdb->prepare(
        "SELECT DATE(visit_date) as date, COUNT(*) as count
        FROM $table_name
        WHERE page_id = %d
        AND visit_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(visit_date)
        ORDER BY date DESC",
        $page_id
    ));

    return array(
        'total_visits' => $total_visits,
        'recent_visits' => $recent_visits,
        'daily_stats' => $daily_stats
    );
}

function e1copy_ai_download_and_convert_to_base64($image_url) {
    if (empty($image_url)) {
        return false;
    }
    if (is_array($image_url)) {
        $image_url = reset($image_url);
    }
    $response = wp_remote_get($image_url);
    if (is_wp_error($response)) {
        return false;
    }
    $image_data = wp_remote_retrieve_body($response);
    if (!$image_data) {
        return false;
    }
    $base64 = 'data:image/png;base64,' . base64_encode($image_data);
    return $base64;
}

// Adicionar hook para registrar visitas
add_action('template_redirect', function() {
    if (is_singular('page')) {
        $page_id = get_the_ID();
        if (get_post_meta($page_id, '_e1copy_page_data', true)) {
            e1copy_ai_record_visit($page_id);
        }
    }
});

// Adicionar estilos apenas nas páginas do plugin
add_action('admin_head', function() {
    // Verificar se estamos em uma página do plugin
    if (function_exists('e1copy_is_plugin_page') && e1copy_is_plugin_page()) {
        echo '<style>
            .wp-list-table th,
            .wp-list-table td {
                vertical-align: middle !important;
                text-align: center !important;
            }
            .wp-list-table .action-icon {
                display: inline-block;
                margin: 0 4px;
                vertical-align: middle;
            }
            .wp-list-table .dashicons {
                vertical-align: middle;
                font-size: 18px;
            }
            .wp-list-table td {
                height: 48px;
            }
        </style>';
    }
});

// Se quiser exibir a imagem, crie uma função e use um shortcode ou hook:
function e1copy_ai_exibir_screenshot($desktop_image_url) {
    if (!empty($desktop_image_url)) {
        $desktop_url = e1copy_ai_download_and_convert_to_base64($desktop_image_url);
        echo '<img src="' . esc_attr($desktop_url) . '" alt="Screenshot Desktop">';
    }
}
// Depois, use [e1copy_ai_screenshot url="URL_DA_IMAGEM"] como shortcode
add_shortcode('e1copy_ai_screenshot', function($atts) {
    $atts = shortcode_atts(['url' => ''], $atts);
    ob_start();
    e1copy_ai_exibir_screenshot($atts['url']);
    return ob_get_clean();
});

add_action('admin_enqueue_scripts', function() {
    // Verificar se estamos em uma página do plugin
    if (function_exists('e1copy_is_plugin_page') && e1copy_is_plugin_page()) {
        wp_enqueue_script('jquery');
        wp_enqueue_script('wp-color-picker');
        wp_enqueue_style('wp-color-picker');
        wp_localize_script('jquery', 'e1copy_vars', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('e1copy_get_page_data')
        ]);
    }
}, 1);

// Adicionar script do modal apenas nas páginas do plugin
add_action('admin_footer', function() {
    // Verificar se estamos em uma página do plugin
    if (!function_exists('e1copy_is_plugin_page') || !e1copy_is_plugin_page()) {
        return;
    }

    // Skip output during REST API requests
    if (defined('REST_REQUEST')) {
        return;
    }

    ?>
    <div id="edit-page-modal" class="e1copy-modal" style="display:none;">
        <div class="e1copy-modal-content">
            <span class="e1copy-modal-close">&times;</span>
            <h2>Editar Página</h2>
            <form method="post" class="edit-page-form">
                <?php wp_nonce_field('e1copy_update_page', '_wpnonce'); ?>
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="page_id" value="">
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="url">URL do Site</label></th>
                        <td>
                            <input type="url" name="url" id="url" required class="regular-text">
                            <p class="description">Digite a URL completa (ex: https://exemplo.com)</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="page_title">Título da Página</label></th>
                        <td>
                            <input type="text" name="page_title" id="page_title" required class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="redirect_url">URL de Redirecionamento</label></th>
                        <td>
                            <input type="url" name="redirect_url" id="redirect_url" required class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label>Redirecionamento Automático</label></th>
                        <td>
                            <label><input type="radio" name="auto_redirect" value="yes"> Sim</label>
                            <label><input type="radio" name="auto_redirect" value="no" checked> Não</label>
                            <div id="redirect_delay_container" style="margin-top:10px;display:none;">
                                <select name="redirect_delay">
                                    <option value="5">5 segundos</option>
                                    <option value="10">10 segundos</option>
                                    <option value="20">20 segundos</option>
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="cookie_text">Texto do Popup de Cookies</label></th>
                        <td>
                            <?php
                            wp_editor(
                                '',
                                'cookie_text',
                                [
                                    'textarea_name' => 'cookie_text',
                                    'textarea_rows' => 5,
                                    'media_buttons' => false,
                                    'teeny'         => true,
                                    'quicktags'     => true
                                ]
                            );
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="button_color">Cor dos Botões/Link (HEX)</label></th>
                        <td>
                            <input type="text" name="button_color" id="button_color" class="color-field" value="#4285F4">
                        </td>
                    </tr>
                </table>
                <div class="preview-container">
                    <h3>Preview</h3>
                    <div class="preview-images">
                        <div class="desktop-preview">
                            <h4>Versão Desktop</h4>
                            <img src="" alt="Preview Desktop">
                        </div>
                        <div class="mobile-preview">
                            <h4>Versão Mobile</h4>
                            <img src="" alt="Preview Mobile">
                        </div>
                    </div>
                </div>
                <p class="submit">
                    <button type="submit" class="button button-primary">Salvar Alterações</button>
                </p>
            </form>
        </div>
    </div>

    <style>
    .e1copy-modal {
        display: none;
        position: fixed;
        z-index: 999999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }
    .e1copy-modal-content {
        background-color: #fff;
        margin: 5% auto;
        padding: 20px;
        width: 80%;
        max-width: 1200px;
        position: relative;
        max-height: 90vh;
        overflow-y: auto;
    }
    .e1copy-modal-close {
        position: absolute;
        right: 20px;
        top: 10px;
        font-size: 28px;
        cursor: pointer;
    }
    .preview-container {
        margin-top: 20px;
        border-top: 1px solid #ddd;
        padding-top: 20px;
    }
    .preview-images {
        display: flex;
        gap: 20px;
    }
    .preview-images > div {
        flex: 1;
    }
    .preview-images img {
        max-width: 100%;
        border: 1px solid #ddd;
    }
    </style>

    <script>
    jQuery(document).ready(function($) {
        // Inicializar color picker
        if ($('.color-field').length) {
            $('.color-field').wpColorPicker();
        }

        // Mostrar/ocultar campo de delay
        $('input[name="auto_redirect"]').change(function() {
            $('#redirect_delay_container').toggle($(this).val() === 'yes');
        });

        // Abrir modal ao clicar no botão de editar
        $('.edit-page').off('click').on('click', function(e) {
            console.log('Botão de editar clicado!', $(this).data('page-id'));
            e.preventDefault();

            // Esconder o spinner global se estiver visível
            if (window.e1copyHideSpinner) {
                window.e1copyHideSpinner();
            } else {
                var overlay = document.getElementById('e1copy-spinner-overlay');
                if (overlay) {
                    overlay.style.display = 'none';
                }
            }

            var pageId = $(this).data('page-id');
            var ajaxurl = (typeof e1copy_vars !== 'undefined') ? e1copy_vars.ajaxurl : window.ajaxurl;
            var nonce = (typeof e1copy_vars !== 'undefined') ? e1copy_vars.nonce : '';
            if (!ajaxurl) {
                console.error('ajaxurl não está definido!');
                return;
            }
            $.ajax({
                url: ajaxurl,
                method: 'POST',
                data: {
                    action: 'e1copy_get_page_data',
                    page_id: pageId,
                    nonce: nonce
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Resposta AJAX:', response);
                    if (response.success) {
                        var data = response.data;
                        $('#edit-page-modal form [name="page_id"]').val(pageId);
                        $('#edit-page-modal form [name="url"]').val(data.url);
                        $('#edit-page-modal form [name="page_title"]').val(data.page_title);
                        $('#edit-page-modal form [name="redirect_url"]').val(data.redirect_url);
                        $('#edit-page-modal form [name="auto_redirect"][value="' + data.auto_redirect + '"]').prop('checked', true);
                        $('#edit-page-modal form [name="redirect_delay"]').val(data.redirect_delay);
                        $('#edit-page-modal form [name="button_color"]').val(data.button_color).change();
                        if (data.post_status) {
                            $('#edit-page-modal form [name="post_status"]').val(data.post_status);
                        }
                        if (typeof tinyMCE !== 'undefined' && tinyMCE.get('cookie_text')) {
                            tinyMCE.get('cookie_text').setContent(data.cookie_text);
                        }
                        $('#edit-page-modal form [name="cookie_text"]').val(data.cookie_text);
                        $('.desktop-preview img').attr('src', data.desktop_url);
                        $('.mobile-preview img').attr('src', data.mobile_url);
                        $('#edit-page-modal').show();
                    } else {
                        alert('Erro ao buscar dados da página para edição: ' + (response.data || response));
                        console.error('Erro AJAX:', response);
                    }
                },
                error: function(xhr, status, error) {
                    alert('Erro de comunicação com o servidor: ' + error);
                    console.error('Erro AJAX:', xhr, status, error);
                }
            });
        });

        // Fechar modal ao clicar no X
        $('.e1copy-modal-close').click(function() {
            $('#edit-page-modal').hide();
        });

        // Fechar modal ao clicar fora
        $(window).click(function(e) {
            if ($(e.target).hasClass('e1copy-modal')) {
                $('.e1copy-modal').hide();
            }
        });

        console.log('<?php echo wp_create_nonce("e1copy_get_page_data"); ?>');
    });
    </script>
    <?php
});

// Removendo a função duplicada e mantendo apenas o add_action
add_action('wp_ajax_e1copy_get_page_data', 'e1copy_ai_get_page_data');