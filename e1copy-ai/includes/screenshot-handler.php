<?php
if (!defined('ABSPATH')) exit;

/**
 * Função para verificar se estamos em uma página do plugin E1Copy
 */
function e1copy_is_plugin_page() {
    // Verifica se estamos em uma página admin do plugin
    return is_admin() && isset($_GET['page']) && strpos($_GET['page'], 'e1copy-ai-') === 0;
}

/**
 * Função para iniciar a sessão apenas quando necessário
 */
function e1copy_maybe_start_session() {
    // Só inicia a sessão se estivermos em uma página do plugin ou em uma ação específica
    if (e1copy_is_plugin_page() ||
        (isset($_POST['action']) && strpos($_POST['action'], 'e1copy_ai_') === 0) ||
        (isset($_GET['action']) && strpos($_GET['action'], 'e1copy_ai_') === 0)) {
        if (!session_id()) {
            session_start();
        }
    }
}

// Iniciar sessão apenas quando necessário
add_action('admin_init', 'e1copy_maybe_start_session', 1);

// Adicionar o spinner e scripts apenas nas páginas do plugin
add_action('admin_footer', 'e1copy_add_spinner_and_scripts');

function e1copy_add_spinner_and_scripts() {
    // Só adiciona o spinner e scripts se estivermos em uma página do plugin
    if (!e1copy_is_plugin_page()) {
        return;
    }

    echo '<div id="e1copy-spinner-overlay" style="display:none;position:fixed;top:0;left:0;width:100vw;height:100vh;background:rgba(0,0,0,0.7);z-index:999999;align-items:center;justify-content:center;display:flex;">
      <div class="e1copy-spinner" style="border: 8px solid #f3f3f3;border-top: 8px solid #4285F4;border-radius: 50%;width: 60px;height: 60px;animation: e1copy-spin 1s linear infinite;"></div>
    </div>';
    ?>
    <style>
    @keyframes e1copy-spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    </style>
    <script>
    // Funções globais para controlar o spinner
    window.e1copyShowSpinner = function() {
      var overlay = document.getElementById('e1copy-spinner-overlay');
      if (overlay) {
        overlay.style.display = 'flex';
      }
    };

    window.e1copyHideSpinner = function() {
      var overlay = document.getElementById('e1copy-spinner-overlay');
      if (overlay) {
        overlay.style.display = 'none';
      }
    };

    document.addEventListener('DOMContentLoaded', function() {
      function showSpinner() {
        window.e1copyShowSpinner();
      }
      function hideSpinner() {
        window.e1copyHideSpinner();
      }

      // Seletor mais específico para evitar conflitos com outros plugins
      var e1copyForms = document.querySelectorAll('.wrap form');
      e1copyForms.forEach(function(form) {
        form.addEventListener('submit', function() {
          showSpinner();
        });
      });

      var e1copyButtons = document.querySelectorAll('.wrap button[type="submit"]');
      e1copyButtons.forEach(function(btn) {
        btn.addEventListener('click', function() {
          showSpinner();
        });
      });

      var e1copyLinks = document.querySelectorAll('.wrap a.button, .wrap a.button-link-delete');
      e1copyLinks.forEach(function(link) {
        // Não mostrar spinner para o botão de documentação da API
        if (link.id === 'open-api-docs') {
          return;
        }

        link.addEventListener('click', function(e) {
          if (!link.target || link.target === '_self') {
            showSpinner();
          }
        });
      });

      // Esconder spinner quando a página estiver carregada
      document.onreadystatechange = function () {
        if (document.readyState === 'interactive') {
          hideSpinner();
        }
      };

      window.onload = function() {
        hideSpinner();
      };

      // Função para esconder o spinner
      function hideE1CopySpinner() {
        var overlay = document.getElementById('e1copy-spinner-overlay');
        if (overlay) overlay.style.display = 'none';
      }

      // Fechar modal ao clicar no X
      if (window.jQuery) {
        jQuery('.e1copy-modal-close').click(function() {
          jQuery('#edit-page-modal').hide();
          hideE1CopySpinner();
        });

        // Fechar modal ao clicar fora
        jQuery(window).click(function(e) {
          if (jQuery(e.target).hasClass('e1copy-modal')) {
            jQuery('.e1copy-modal').hide();
            hideE1CopySpinner();
          }
        });
      }
    });
    </script>
    <?php
}

/**
 * Função para capturar screenshot usando a API do ApiFlash
 */
function e1copy_ai_capture_screenshot($url, $device = 'desktop') {
    // Verificar se a API está válida (verificação em tempo real)
    if (!e1copy_ai_is_activated(true)) {
        return new WP_Error('api_invalid', 'Licença do E1Copy AI inválida ou suspensa. Verifique sua licença em app.melhorcupom.shop');
    }

    $api_key = get_option('e1copy_ai_apiflash_key');
    if (empty($api_key)) {
        return new WP_Error('api_key_missing', 'Chave API do ApiFlash não configurada');
    }
    $params = [
        'access_key' => $api_key,
        'url' => $url,
        'delay' => 3,
        'format' => 'webp',
        'quality' => 100,
        'fresh' => 'true',
        'response_type' => 'json'
    ];
    if ($device === 'mobile') {
        $params += [
            'user_agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
            'width' => 375,
            'height' => 667
        ];
    } else {
        $params += [
            'width' => 1920,
            'height' => 1080
        ];
    }
    $api_url = 'https://api.apiflash.com/v1/urltoimage?' . http_build_query($params);
    $response = wp_remote_get($api_url, ['timeout' => 30]);
    if (is_wp_error($response)) {
        return $response;
    }
    $body = json_decode(wp_remote_retrieve_body($response), true);
    if (!isset($body['url'])) {
        return new WP_Error('api_error', 'Resposta inesperada da API');
    }
    // Baixar a imagem e converter para base64
    $img_response = wp_remote_get($body['url']);
    if (is_wp_error($img_response)) {
        return $img_response;
    }
    $img_data = wp_remote_retrieve_body($img_response);
    if (!$img_data) {
        return new WP_Error('img_error', 'Não foi possível baixar a imagem');
    }
    $base64 = 'data:image/webp;base64,' . base64_encode($img_data);
    return $base64;
}

// Registrar handler para o form
add_action('admin_post_e1copy_ai_capture_screenshot', 'e1copy_ai_handle_screenshot_capture');

/**
 * Processa a requisição de captura de screenshot
 */
function e1copy_ai_handle_screenshot_capture() {
    // Verificar se a API está válida (verificação em tempo real)
    e1copy_ai_check_api_access_realtime('Clonagem de Páginas');

    // Verificar nonce e permissões
    if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'e1copy_ai_screenshot_nonce')) {
        wp_die('Ação não autorizada.');
    }

    if (!current_user_can('manage_options')) {
        wp_die('Permissão negada.');
    }

    // Validar entrada
    $data = [
        'url' => esc_url_raw($_POST['url']),
        'page_title' => sanitize_text_field($_POST['page_title'] ?? 'Informe o título da página'),
        'redirect_url' => esc_url_raw($_POST['redirect_url'] ?? 'https://seulinkdahotmart.com.br'),
        'auto_redirect' => isset($_POST['auto_redirect']) ? sanitize_text_field($_POST['auto_redirect']) : 'no',
        'redirect_delay' => in_array($_POST['redirect_delay'], ['5', '10', '20']) ? (int)$_POST['redirect_delay'] : 5,
        'cookie_text' => sanitize_textarea_field($_POST['cookie_text'] ?? 'Este site usa cookies para melhorar sua experiência.'),
        'button_color' => sanitize_hex_color($_POST['button_color'] ?? '#4285F4')
    ];

    // Armazenar dados na sessão
    $_SESSION['e1copy_page_data'] = $data;

    // Redirecionar para a mesma página com parâmetro de preview
    wp_redirect(admin_url('admin.php?page=e1copy-ai-screenshot&action=preview'));
    exit;
}

/**
 * Exibe a página de administração
 */
function e1copy_ai_screenshot_page() {
    // Verificar se a API está válida (verificação em tempo real)
    e1copy_ai_check_api_access_realtime('Clonagem de Páginas');

    // Processar salvamento da página
    if (isset($_GET['action']) && $_GET['action'] === 'save' && isset($_SESSION['e1copy_page_data'])) {
        // Verificar nonce
        if (!isset($_POST['_wpnonce']) || !wp_verify_nonce($_POST['_wpnonce'], 'e1copy_ai_save_page')) {
            wp_die('Ação não autorizada.');
        }

        $data = $_SESSION['e1copy_page_data'];

        // Criar o HTML da página
        $html = '<!DOCTYPE html><html><head><title>'.esc_html($data['page_title']).'</title>';
        $html .= '<style>body{margin:0;padding:0;overflow:hidden}</style>';
        $html .= '</head><body>';
        $html .= '<div style="position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.7)"></div>';
        $html .= '<img id="desktopImg" src="'.esc_url($data['desktop_url']).'" style="display:block;width:100%;height:100%;object-fit:cover">';
        $html .= '<img id="mobileImg" src="'.esc_url($data['mobile_url']).'" style="display:none;width:100%;height:100%;object-fit:cover">';
        $html .= '<script>
            if(window.matchMedia("(max-width:768px)").matches) {
                document.getElementById("desktopImg").style.display = "none";
                document.getElementById("mobileImg").style.display = "block";
            }
        </script>';

        // Redirecionamento automático
        if ($data['auto_redirect'] === 'yes' && !empty($data['redirect_url'])) {
            $html .= '<script>
                setTimeout(function() {
                    window.location.href = "'.esc_url($data['redirect_url']).'";
                }, '.($data['redirect_delay'] * 1000).');
            </script>';
        }

        // Popup de consentimento de cookies
        $html .= '<div id="cookie-consent" style="position:fixed;bottom:20px;left:50%;transform:translateX(-50%);background:#fff;padding:20px 30px;border-radius:8px;box-shadow:0 2px 8px rgba(0,0,0,0.2);z-index:9999;display:block;">
            <span>'.wp_kses_post($data['cookie_text']).'</span>
            <button id="accept-cookie" style="margin-left:20px;background:'.esc_attr($data['button_color']).';color:#fff;border:none;padding:8px 16px;border-radius:4px;cursor:pointer;">Aceitar</button>
        </div>
        <script>
            document.getElementById("accept-cookie").onclick = function() {
                document.getElementById("cookie-consent").style.display = "none";
            }
        </script>';

        $html .= '</body></html>';

        // Criar a página no WordPress
        $page_data = [
            'post_title' => $data['page_title'],
            'post_content' => $html,
            'post_status' => 'draft',
            'post_type' => 'page'
        ];

        $page_id = wp_insert_post($page_data);

        if (!is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', 'elementor_canvas');
            update_post_meta($page_id, '_e1copy_page_data', $data);
            unset($_SESSION['e1copy_page_data']);
            wp_redirect(admin_url('admin.php?page=e1copy-ai-my-pages&message=created'));
            exit;
        }

        wp_die('Erro ao salvar página: '.$page_id->get_error_message());
    }

    // Mostrar preview se existirem dados na sessão
    if (isset($_GET['action']) && $_GET['action'] === 'preview' && isset($_SESSION['e1copy_page_data'])) {
        $data = $_SESSION['e1copy_page_data'];

        // Capturar screenshots
        $data['desktop_url'] = e1copy_ai_capture_screenshot($data['url'], 'desktop');
        $data['mobile_url'] = e1copy_ai_capture_screenshot($data['url'], 'mobile');

        if (is_wp_error($data['desktop_url']) || is_wp_error($data['mobile_url'])) {
            wp_die('Erro ao capturar screenshots: '.(is_wp_error($data['desktop_url']) ? $data['desktop_url']->get_error_message() : $data['mobile_url']->get_error_message()));
        }

        // Atualizar dados na sessão
        $_SESSION['e1copy_page_data'] = $data;
        ?>
        <div class="wrap">
            <h1>Pré-visualização da Página</h1>

            <div id="e1copy-loader" style="display:flex;align-items:center;justify-content:center;height:200px;">
                <div class="e1copy-spinner"></div>
            </div>
            <style>
            .e1copy-spinner {
                border: 8px solid #f3f3f3;
                border-top: 8px solid #4285F4;
                border-radius: 50%;
                width: 60px;
                height: 60px;
                animation: e1copy-spin 1s linear infinite;
            }
            @keyframes e1copy-spin {
                0% { transform: rotate(0deg);}
                100% { transform: rotate(360deg);}
            }
            </style>

            <div style="display:flex;gap:20px;margin-bottom:30px;">
                <div style="flex:1;">
                    <h3>Versão Desktop</h3>
                    <img src="<?php echo $data['desktop_url']; ?>" style="max-width:100%;border:1px solid #ddd;">
                </div>
                <div style="flex:1;">
                    <h3>Versão Mobile</h3>
                    <img src="<?php echo $data['mobile_url']; ?>" style="max-width:300px;border:1px solid #ddd;">
                </div>
            </div>

            <form method="post" action="<?php echo admin_url('admin.php?page=e1copy-ai-screenshot&action=save'); ?>">
                <?php wp_nonce_field('e1copy_ai_save_page'); ?>
                <button type="submit" class="button button-primary">Salvar Página</button>
                <a href="<?php echo admin_url('admin.php?page=e1copy-ai-screenshot'); ?>" class="button">Cancelar</a>
            </form>
        </div>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            var desktopImg = document.querySelector('img[src="<?php echo $data['desktop_url']; ?>"]');
            var mobileImg = document.querySelector('img[src="<?php echo $data['mobile_url']; ?>"]');
            var loader = document.getElementById('e1copy-loader');
            var loaded = 0;
            function hideLoader() {
                loaded++;
                if (loaded === 2) {
                    loader.style.display = 'none';
                }
            }
            if (desktopImg) desktopImg.addEventListener('load', hideLoader);
            if (mobileImg) mobileImg.addEventListener('load', hideLoader);
        });
        // Garantia extra: esconder loader quando o documento estiver 90% carregado
        document.onreadystatechange = function () {
            if (document.readyState === 'interactive' || document.readyState === 'complete') {
                var loader = document.getElementById('e1copy-loader');
                if (loader) loader.style.display = 'none';
            }
        };
        </script>
        <?php
        return;
    }

    // Formulário principal
    ?>
    <div class="wrap">
        <h1>Clonar Página</h1>

        <form method="post" action="<?php echo admin_url('admin-post.php'); ?>">
            <input type="hidden" name="action" value="e1copy_ai_capture_screenshot">
            <?php wp_nonce_field('e1copy_ai_screenshot_nonce'); ?>

            <table class="form-table">
                <tr>
                    <th scope="row"><label for="url">URL do Site</label></th>
                    <td>
                        <input type="url" name="url" id="url" required class="regular-text">
                        <p class="description">Digite a URL completa (ex: https://exemplo.com)</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="page_title">Título da Página</label></th>
                    <td>
                        <input type="text" name="page_title" id="page_title" required class="regular-text" value="Informe o título da página">
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="redirect_url">URL de Redirecionamento</label></th>
                    <td>
                        <input type="url" name="redirect_url" id="redirect_url" required class="regular-text" value="https://seulinkdahotmart.com.br">
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label>Redirecionamento Automático</label></th>
                    <td>
                        <label><input type="radio" name="auto_redirect" value="yes"> Sim</label>
                        <label><input type="radio" name="auto_redirect" value="no" checked> Não</label>
                        <div id="redirect_delay_container" style="margin-top:10px;display:none;">
                            <select name="redirect_delay">
                                <option value="5">5 segundos</option>
                                <option value="10">10 segundos</option>
                                <option value="20">20 segundos</option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="cookie_text">Texto do Popup de Cookies</label></th>
                    <td>
                        <?php
                        wp_editor(
                            '<h2>Este site usa cookies</h2>Este site usa cookies para personalizar conteúdo e anúncios, fornecer recursos de mídia social e analisar nosso tráfego. Ao clicar em Aceitar, você concorda com o uso de cookies. Para mais informações, por favor visite nossa <a href="#">Política de Cookies</a>.',
                            'cookie_text',
                            [
                                'textarea_name' => 'cookie_text',
                                'textarea_rows' => 5,
                                'media_buttons' => false,
                                'teeny'         => true,
                                'quicktags'     => true
                            ]
                        );
                        ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row"><label for="button_color">Cor dos Botões/Link (HEX)</label></th>
                    <td>
                        <input type="text" name="button_color" id="button_color" class="color-field" value="#4285F4">
                    </td>
                </tr>
            </table>

            <?php submit_button('Clonar Página'); ?>
        </form>
    </div>

    <script>
        jQuery(document).ready(function($) {
            // Mostrar/ocultar campo de delay
            $('input[name="auto_redirect"]').change(function() {
                $('#redirect_delay_container').toggle($(this).val() === 'yes');
            });

            // Inicializar color picker
            if ($('.color-field').length) {
                $('.color-field').wpColorPicker();
            }
        });
    </script>
    <?php
}

/**
 * Registrar configurações
 */
add_action('admin_init', 'e1copy_ai_register_screenshot_settings');

function e1copy_ai_register_screenshot_settings() {
    register_setting('e1copy_ai_settings', 'e1copy_ai_apiflash_key');

    add_settings_section(
        'e1copy_ai_screenshot_section',
        'Configurações de Screenshot',
        'e1copy_ai_screenshot_section_cb',
        'e1copy-ai-settings'
    );

    add_settings_field(
        'e1copy_ai_apiflash_key',
        'API Key do ApiFlash',
        'e1copy_ai_apiflash_key_cb',
        'e1copy-ai-settings',
        'e1copy_ai_screenshot_section'
    );
}

function e1copy_ai_screenshot_section_cb() {
    echo '<p>Configure sua chave de API do <a href="https://apiflash.com/" target="_blank">ApiFlash</a> para habilitar a captura de screenshots.</p>';
}

function e1copy_ai_apiflash_key_cb() {
    $key = get_option('e1copy_ai_apiflash_key');
    echo '<input type="password" name="e1copy_ai_apiflash_key" value="' . esc_attr($key) . '" class="regular-text">';
}

// Registrar scripts e estilos necessários
add_action('admin_enqueue_scripts', function() {
    wp_enqueue_style('wp-color-picker');
    wp_enqueue_script('wp-color-picker');
});