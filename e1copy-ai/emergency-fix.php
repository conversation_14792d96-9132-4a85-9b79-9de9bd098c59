<?php
/**
 * <PERSON>ript de emergência para corrigir posts afetados pelo plugin E1Copy AI
 * Execute este arquivo no WordPress para restaurar posts com problemas
 */

// Verificar se estamos no WordPress
if (!defined('ABSPATH')) {
    die('Este script deve ser executado dentro do WordPress');
}

echo "<h2>Script de Emergência - Correção de Posts Afetados</h2>";

global $wpdb;

// 1. Encontrar posts que podem ter sido afetados incorretamente
echo "<h3>1. Identificando posts afetados</h3>";

$affected_posts = $wpdb->get_results(
    "SELECT ID, post_title, post_content FROM {$wpdb->posts} 
     WHERE post_type = 'post' 
     AND post_status = 'publish' 
     AND (
         post_content LIKE '%data-e1copy-processed%' OR
         post_content LIKE '%style=\"text-decoration: none; color: inherit;\"%'
     )
     AND ID NOT IN (
         SELECT post_id FROM {$wpdb->postmeta} 
         WHERE meta_key = '_e1copy_created_by_plugin' 
         AND meta_value = '1'
     )",
    ARRAY_A
);

if (empty($affected_posts)) {
    echo "✅ Nenhum post afetado incorretamente encontrado.<br>";
} else {
    echo "⚠️ Encontrados " . count($affected_posts) . " posts que podem ter sido afetados:<br>";
    
    foreach ($affected_posts as $post_data) {
        echo "<h4>Post ID {$post_data['ID']}: {$post_data['post_title']}</h4>";
        
        // Limpar o conteúdo do post
        $original_content = $post_data['post_content'];
        $clean_content = $original_content;
        
        // Remover marcadores do plugin
        $clean_content = str_replace(
            [
                '<span data-e1copy-processed="true" style="display: none;"></span>',
                'data-e1copy-processed="true"',
                'style="display: none;"'
            ],
            ['', '', ''],
            $clean_content
        );
        
        // Remover links de afiliados adicionados incorretamente
        // Padrão para encontrar links com o estilo específico do plugin
        $pattern = '/<a href="[^"]*" target="_blank" rel="nofollow" style="text-decoration: none; color: inherit;">(<img[^>]*>)<\/a>/i';
        $clean_content = preg_replace($pattern, '$1', $clean_content);
        
        // Verificar se houve mudanças
        if ($clean_content !== $original_content) {
            // Atualizar o post
            $result = wp_update_post([
                'ID' => $post_data['ID'],
                'post_content' => $clean_content
            ]);
            
            if ($result && !is_wp_error($result)) {
                echo "✅ Post corrigido com sucesso<br>";
            } else {
                echo "❌ Erro ao corrigir o post<br>";
            }
        } else {
            echo "ℹ️ Nenhuma correção necessária<br>";
        }
    }
}

// 2. Verificar e corrigir afiliados com problemas
echo "<h3>2. Verificando afiliados</h3>";

$table_name = $wpdb->prefix . 'e1copy_affiliates';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    // Verificar afiliados com conteúdo vazio
    $empty_content_affiliates = $wpdb->get_results(
        "SELECT id, title, content FROM $table_name WHERE content = '' OR content IS NULL",
        ARRAY_A
    );
    
    if (!empty($empty_content_affiliates)) {
        echo "⚠️ Encontrados " . count($empty_content_affiliates) . " afiliados com conteúdo vazio:<br>";
        
        foreach ($empty_content_affiliates as $affiliate) {
            echo "- Afiliado ID {$affiliate['id']}: {$affiliate['title']}<br>";
            
            // Regenerar conteúdo usando a função do plugin
            if (function_exists('e1copy_ai_generate_template_content')) {
                $affiliate_data = $wpdb->get_row(
                    $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $affiliate['id']),
                    ARRAY_A
                );
                
                if ($affiliate_data) {
                    $new_content = e1copy_ai_generate_template_content($affiliate_data);
                    
                    if (!empty($new_content)) {
                        $wpdb->update(
                            $table_name,
                            ['content' => $new_content],
                            ['id' => $affiliate['id']]
                        );
                        echo "  ✅ Conteúdo regenerado<br>";
                    } else {
                        echo "  ❌ Falha ao regenerar conteúdo<br>";
                    }
                }
            }
        }
    } else {
        echo "✅ Todos os afiliados têm conteúdo válido<br>";
    }
    
    // Verificar botões com texto antigo
    $old_button_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE button_text = 'Ver Produto'");
    echo "Afiliados com botão 'Ver Produto': $old_button_count<br>";
    
    if ($old_button_count > 0) {
        echo "ℹ️ Estes afiliados mantêm o texto original conforme solicitado<br>";
    }
    
} else {
    echo "❌ Tabela de afiliados não encontrada<br>";
}

// 3. Verificar configurações do plugin
echo "<h3>3. Verificando configurações do plugin</h3>";

// Verificar se o filtro the_content está desabilitado
if (has_filter('the_content', 'e1copy_ai_add_affiliate_links_to_images')) {
    echo "⚠️ Filtro the_content ainda está ativo - pode causar problemas<br>";
} else {
    echo "✅ Filtro the_content está desabilitado<br>";
}

// 4. Limpar transients relacionados
echo "<h3>4. Limpando cache</h3>";

delete_transient('e1copy_posts_checked');
echo "✅ Cache limpo<br>";

echo "<h3>Correção Concluída</h3>";
echo "<p><strong>Recomendações:</strong></p>";
echo "<ul>";
echo "<li>Verifique se os posts afetados estão exibindo corretamente</li>";
echo "<li>Teste a criação de novos afiliados</li>";
echo "<li>Novos afiliados devem usar 'Comprar' como texto padrão do botão</li>";
echo "<li>Posts existentes não devem ser mais afetados pelo plugin</li>";
echo "</ul>";
?>
