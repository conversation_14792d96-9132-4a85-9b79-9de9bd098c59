<?php
/**
 * Script de teste para verificar as correções do plugin E1Copy AI
 * Execute este arquivo no WordPress para verificar se as correções estão funcionando
 */

// Verificar se estamos no WordPress
if (!defined('ABSPATH')) {
    die('Este script deve ser executado dentro do WordPress');
}

echo "<h2>Teste das Correções do Plugin E1Copy AI</h2>";

// 1. Verificar se o valor padrão do botão foi alterado
echo "<h3>1. Verificando valor padrão do botão</h3>";

global $wpdb;
$table_name = $wpdb->prefix . 'e1copy_affiliates';

// Verificar se a tabela existe
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo "✅ Tabela de afiliados existe<br>";
    
    // Verificar a estrutura da coluna button_text
    $column_info = $wpdb->get_row("SHOW COLUMNS FROM $table_name LIKE 'button_text'");
    
    if ($column_info) {
        echo "✅ Coluna button_text existe<br>";
        echo "Valor padrão: " . ($column_info->Default ?: 'NULL') . "<br>";
        
        if (strpos($column_info->Default, 'Comprar') !== false) {
            echo "✅ Valor padrão correto: 'Comprar'<br>";
        } else {
            echo "❌ Valor padrão incorreto. Esperado: 'Comprar', Atual: " . $column_info->Default . "<br>";
        }
    } else {
        echo "❌ Coluna button_text não existe<br>";
    }
    
    // Verificar registros existentes
    $old_records = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE button_text = 'Ver Produto'");
    $new_records = $wpdb->get_var("SELECT COUNT(*) FROM $table_name WHERE button_text = 'Comprar'");
    
    echo "Registros com 'Ver Produto': $old_records<br>";
    echo "Registros com 'Comprar': $new_records<br>";
    
    if ($old_records > 0) {
        echo "⚠️ Ainda existem registros com 'Ver Produto'. Execute a atualização do banco de dados.<br>";
    } else {
        echo "✅ Todos os registros foram atualizados<br>";
    }
} else {
    echo "❌ Tabela de afiliados não existe<br>";
}

// 2. Verificar se o filtro the_content está funcionando corretamente
echo "<h3>2. Verificando filtro the_content</h3>";

if (has_filter('the_content', 'e1copy_ai_add_affiliate_links_to_images')) {
    echo "✅ Filtro the_content está registrado<br>";
} else {
    echo "❌ Filtro the_content não está registrado<br>";
}

// 3. Verificar se a função de verificação de posts existe
echo "<h3>3. Verificando função de verificação de posts</h3>";

if (function_exists('e1copy_ai_check_and_fix_existing_posts')) {
    echo "✅ Função de verificação de posts existe<br>";
} else {
    echo "❌ Função de verificação de posts não existe<br>";
}

// 4. Verificar se há posts afetados incorretamente
echo "<h3>4. Verificando posts afetados incorretamente</h3>";

$affected_posts = $wpdb->get_results(
    "SELECT ID, post_title FROM {$wpdb->posts} 
     WHERE post_type = 'post' 
     AND post_status = 'publish' 
     AND post_content LIKE '%data-e1copy-processed%'
     AND ID NOT IN (
         SELECT post_id FROM {$wpdb->postmeta} 
         WHERE meta_key = '_e1copy_created_by_plugin' 
         AND meta_value = '1'
     )",
    ARRAY_A
);

if (empty($affected_posts)) {
    echo "✅ Nenhum post afetado incorretamente encontrado<br>";
} else {
    echo "⚠️ Encontrados " . count($affected_posts) . " posts que podem ter sido afetados:<br>";
    foreach ($affected_posts as $post) {
        echo "- Post ID {$post['ID']}: {$post['post_title']}<br>";
    }
}

// 5. Verificar posts criados pelo plugin
echo "<h3>5. Verificando posts criados pelo plugin</h3>";

$plugin_posts = $wpdb->get_results(
    "SELECT post_id FROM {$wpdb->postmeta} 
     WHERE meta_key = '_e1copy_created_by_plugin' 
     AND meta_value = '1'",
    ARRAY_A
);

echo "Posts criados pelo plugin: " . count($plugin_posts) . "<br>";

if (!empty($plugin_posts)) {
    echo "IDs dos posts: ";
    $ids = array_column($plugin_posts, 'post_id');
    echo implode(', ', $ids) . "<br>";
}

// 6. Verificar se os estilos CSS estão registrados
echo "<h3>6. Verificando estilos CSS</h3>";

if (wp_style_is('e1copy-affiliate-custom-css', 'registered')) {
    echo "✅ Estilos CSS personalizados estão registrados<br>";
} else {
    echo "❌ Estilos CSS personalizados não estão registrados<br>";
}

echo "<h3>Teste Concluído</h3>";
echo "<p>Se você encontrou problemas, verifique os logs de erro do WordPress para mais detalhes.</p>";
