# E1Copy AI

Plugin WordPress para clonar páginas, gerar screenshots e criar páginas otimizadas com IA.

## Requisitos

- **WordPress:** 5.8 ou superior
- **PHP:** 7.4 ou superior (recomendado PHP 8+)
- **Extensões PHP:**
  - curl
  - json
  - mbstring (recomendado)
- **Sessão PHP:** habilitada
- **Chave de API do ApiFlash:** [https://apiflash.com/](https://apiflash.com/)
- **(Opcional) Elementor:** Para usar o template "Elementor Canvas" nas páginas clonadas

## Instalação

1. Faça upload da pasta do plugin para o diretório `wp-content/plugins/` do seu WordPress.
2. Ative o plugin no menu "Plugins" do WordPress.
3. Acesse o menu "E1Copy AI" no painel administrativo.

## Configuração

1. Vá em **E1Copy AI > Configurações**.
2. Insira sua chave de API do ApiFlash.
3. Salve as configurações.

## Como usar

### Clonar Página
1. Acesse **E1Copy AI > Clonar Página**.
2. Preencha os campos:
   - URL do site a ser clonado
   - Título da página
   - URL de redirecionamento (opcional)
   - Redirecionamento automático (opcional)
   - Texto do popup de cookies (com editor visual)
   - Cor dos botões/links
3. Clique em "Clonar Página".
4. Visualize e salve a página clonada.

### Gerenciar Páginas
- Acesse **E1Copy AI > Minhas Páginas** para editar, visualizar ou remover páginas clonadas.
- O editor visual permite alterar o texto do popup de cookies com HTML e formatação.

## Funcionalidades
- Clonagem de páginas com screenshots (desktop e mobile)
- Redirecionamento automático configurável
- Popup de consentimento de cookies personalizável
- Editor visual para o texto do popup
- Compatível com Elementor (opcional)
- Spinner de carregamento para melhor experiência do usuário

## Observações
- O plugin não depende do Elementor, mas se instalado, usa o template "Elementor Canvas" para páginas clonadas.
- O popup de cookies sempre aparece ao acessar a página clonada, sem salvar aceitação no navegador.
- O plugin utiliza sessões PHP para armazenar dados temporários durante o processo de clonagem.

## Suporte
Para dúvidas, sugestões ou problemas, entre em contato com o desenvolvedor.

---
**Desenvolvido por Esmael Silva** 