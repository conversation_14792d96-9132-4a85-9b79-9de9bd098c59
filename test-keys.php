<?php
/**
 * <PERSON>ript para testar as chaves de API
 */

// Testar via cURL direto
echo "=== Teste de Chaves de API ===\n\n";

// Teste 1: Endpoint de teste (sem autenticação)
echo "1. Testando endpoint de teste...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://melhorcupom.shop/wp-json/e1copy-ai/v1/test');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Status: $httpCode\n";
echo "Resposta: $response\n\n";

// Teste 2: Verificação de chave sem chave
echo "2. Testando verificação sem chave...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://melhorcupom.shop/wp-json/e1copy-ai/v1/verify-key');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Status: $httpCode\n";
echo "Resposta: $response\n\n";

// Teste 3: Tentar algumas chaves comuns
$test_keys = [
    'e1copy_1234567890abcdef1234567890abcdef',
    'fPPyj',
    'test123',
    'api_key_test'
];

foreach ($test_keys as $key) {
    echo "3. Testando chave: $key\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://melhorcupom.shop/wp-json/e1copy-ai/v1/verify-key');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'X-E1Copy-API-Key: ' . $key,
        'Accept: application/json'
    ]);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Status: $httpCode\n";
    echo "Resposta: $response\n\n";
}

// Teste 4: Verificar se o endpoint de posts funciona
echo "4. Testando endpoint de posts...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://melhorcupom.shop/wp-json/e1copy-ai/v1/posts');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Status: $httpCode\n";
echo "Resposta: $response\n\n";

echo "=== Teste concluído ===\n";
?>
